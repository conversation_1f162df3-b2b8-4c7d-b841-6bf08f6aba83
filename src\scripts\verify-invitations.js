// Script to verify the invitation system setup
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function verifyInvitationSystem() {
  try {
    console.log('Verifying invitation system setup...');
    
    // Check if user_invitations table exists
    console.log('\n=== User Invitations Table ===');
    try {
      const { data: invitations, error: invitationsError } = await supabase
        .from('user_invitations')
        .select('*')
        .limit(5);
      
      if (invitationsError) {
        console.error('Error fetching invitations:', invitationsError);
        console.log('The user_invitations table may not exist or you may not have access to it.');
      } else {
        console.log('The user_invitations table exists!');
        console.log(`Found ${invitations.length} invitations`);
        
        if (invitations.length > 0) {
          console.log('\nSample invitation:');
          const sample = invitations[0];
          console.log(`ID: ${sample.id}`);
          console.log(`Email: ${sample.email}`);
          console.log(`Organization ID: ${sample.organization_id}`);
          console.log(`Role: ${sample.role}`);
          console.log(`Status: ${sample.status}`);
          console.log(`Token: ${sample.token}`);
          console.log(`Created: ${new Date(sample.created_at).toLocaleString()}`);
          console.log(`Expires: ${new Date(sample.expires_at).toLocaleString()}`);
        }
      }
    } catch (error) {
      console.error('Error checking user_invitations table:', error);
    }
    
    // Check if the invite_user function exists
    console.log('\n=== Invite User Function ===');
    try {
      // We'll try to call the function with test parameters
      const { data: inviteResult, error: inviteError } = await supabase
        .rpc('invite_user', {
          email_param: '<EMAIL>',
          organization_id_param: '1b71a481-7a6f-4be6-8e46-c96f98790e4c', // Use the first Test Organization ID
          role_param: 'teacher'
        });
      
      if (inviteError) {
        console.error('Error testing invite_user function:', inviteError);
        
        if (inviteError.message.includes('permission denied')) {
          console.log('The function exists but requires admin privileges to call.');
        } else if (inviteError.message.includes('does not exist')) {
          console.log('The invite_user function does not exist.');
        } else {
          console.log('There was an error with the invite_user function.');
        }
      } else {
        console.log('The invite_user function exists and works!');
        console.log(`Created invitation with ID: ${inviteResult}`);
        
        // Check if the invitation was actually created
        const { data: newInvite, error: fetchError } = await supabase
          .from('user_invitations')
          .select('*')
          .eq('id', inviteResult)
          .single();
        
        if (fetchError) {
          console.error('Error fetching the new invitation:', fetchError);
        } else {
          console.log('\nNew invitation details:');
          console.log(`Email: ${newInvite.email}`);
          console.log(`Organization ID: ${newInvite.organization_id}`);
          console.log(`Role: ${newInvite.role}`);
          console.log(`Token: ${newInvite.token}`);
        }
      }
    } catch (error) {
      console.error('Error testing invite_user function:', error);
    }
    
    // Check if the accept_invitation function exists
    console.log('\n=== Accept Invitation Function ===');
    try {
      // Get a test invitation token
      const { data: testInvites, error: testInviteError } = await supabase
        .from('user_invitations')
        .select('token')
        .eq('status', 'pending')
        .limit(1);
      
      if (testInviteError || !testInvites || testInvites.length === 0) {
        console.log('No pending invitations found to test with.');
        console.log('Testing with a dummy token instead.');
        
        const { data: acceptResult, error: acceptError } = await supabase
          .rpc('accept_invitation', {
            token_param: 'dummy_token',
            user_id_param: 'e0eb9971-8690-4b51-b0d6-04805f2955ab'
          });
        
        if (acceptError) {
          if (acceptError.message.includes('does not exist')) {
            console.log('The accept_invitation function does not exist.');
          } else {
            console.log('The accept_invitation function exists but returned an error:', acceptError.message);
          }
        } else {
          console.log('The accept_invitation function exists and is callable!');
          console.log('Result:', acceptResult);
        }
      } else {
        const testToken = testInvites[0].token;
        console.log(`Found a test invitation token: ${testToken}`);
        
        const { data: acceptResult, error: acceptError } = await supabase
          .rpc('accept_invitation', {
            token_param: testToken,
            user_id_param: 'e0eb9971-8690-4b51-b0d6-04805f2955ab'
          });
        
        if (acceptError) {
          console.log('Error testing accept_invitation function:', acceptError.message);
        } else {
          console.log('The accept_invitation function exists and works!');
          console.log('Result:', acceptResult);
          
          // Check if the invitation status was updated
          const { data: updatedInvite, error: updateError } = await supabase
            .from('user_invitations')
            .select('status')
            .eq('token', testToken)
            .single();
          
          if (updateError) {
            console.error('Error checking invitation status:', updateError);
          } else {
            console.log(`Invitation status is now: ${updatedInvite.status}`);
          }
        }
      }
    } catch (error) {
      console.error('Error testing accept_invitation function:', error);
    }
    
    console.log('\nVerification complete');
  } catch (error) {
    console.error('Error verifying invitation system:', error);
  }
}

verifyInvitationSystem();
