/**
 * TaskTimeline Component
 *
 * This component implements the "always mount, conditionally render" pattern.
 * It renders the appropriate timeline based on the task type.
 */

import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, Clock } from 'lucide-react';
import { Task, isInternalTask, isExternalTask } from '@/types/tasks';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

// Define status descriptions for internal tasks
const INTERNAL_STATUS_DESCRIPTIONS = {
  'assigned': 'Task has been assigned to a staff member',
  'in_progress': 'Staff member is working on the task',
  'completed': 'Task has been marked as completed by the staff member',
  'confirmed': 'Task completion has been confirmed by the admin'
};

// Define status descriptions for external tasks
const EXTERNAL_STATUS_DESCRIPTIONS = {
  'open': 'Task is open for offers from suppliers',
  'interest': 'Suppliers have expressed interest in this task',
  'questions': 'Suppliers are asking questions about this task',
  'offer': 'Suppliers have submitted offers for this task',
  'assigned': 'Task has been assigned to a supplier',
  'in_progress': 'Supplier is working on the task',
  'completed': 'Task has been marked as completed by the supplier',
  'confirmed': 'Task completion has been confirmed by the admin',
  'pending_payment': 'Payment is required to complete this task'
};

// Define status order for internal tasks
const INTERNAL_STATUS_ORDER = [
  'assigned',
  'in_progress',
  'completed',
  'confirmed'
];

// Define status order for external tasks
const EXTERNAL_STATUS_ORDER = [
  'open',
  'interest',
  'questions',
  'offer',
  'assigned',
  'in_progress',
  'completed',
  'confirmed',
  'pending_payment'
];

interface TaskTimelineProps {
  task: Task | null;
  isLoading: boolean;
  error?: Error | null;
  className?: string;
}

/**
 * TaskTimeline component that always mounts but conditionally renders content
 */
const TaskTimeline: React.FC<TaskTimelineProps> = ({
  task,
  isLoading,
  error,
  className
}) => {
  // Early return for loading state
  if (isLoading) {
    return (
      <Card className={cn("w-full mb-6", className)}>
        <CardHeader className="pb-2">
          <Skeleton className="h-6 w-48" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-20 w-full" />
        </CardContent>
      </Card>
    );
  }

  // Early return for error state
  if (error) {
    return (
      <Alert variant="destructive" className="mb-6">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to load task timeline: {error.message}
        </AlertDescription>
      </Alert>
    );
  }

  // Early return if no task
  if (!task) {
    return null;
  }

  // Determine which status order and descriptions to use based on task type
  const statusOrder = isInternalTask(task) ? INTERNAL_STATUS_ORDER : EXTERNAL_STATUS_ORDER;
  const statusDescriptions = isInternalTask(task) ? INTERNAL_STATUS_DESCRIPTIONS : EXTERNAL_STATUS_DESCRIPTIONS;

  // Get the current status index
  // If the status is not in the status order, default to the first status
  const currentStatusIndex = statusOrder.indexOf(task.status) !== -1
    ? statusOrder.indexOf(task.status)
    : 0;

  return (
    <Card className={cn("w-full mb-6", className)}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center">
          <div className="mr-2 p-1 rounded-full bg-blue-100">
            <Clock size={18} className="text-blue-600" />
          </div>
          Task Timeline
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-3 top-0 bottom-0 w-0.5 bg-gray-200" />

          {/* Timeline steps */}
          {statusOrder.map((status, index) => {
            const isCompleted = index <= currentStatusIndex;
            const isCurrent = index === currentStatusIndex;

            return (
              <div key={status} className="relative pl-10 pb-6 last:pb-0">
                {/* Status indicator */}
                <div className={cn(
                  "absolute left-0 w-6 h-6 rounded-full border-2 flex items-center justify-center",
                  isCompleted
                    ? "bg-green-100 border-green-500 text-green-500"
                    : "bg-gray-100 border-gray-300 text-gray-400"
                )}>
                  {isCompleted ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  ) : (
                    <span className="text-xs">{index + 1}</span>
                  )}
                </div>

                {/* Status content */}
                <div>
                  <h4 className={cn(
                    "font-medium",
                    isCurrent ? "text-blue-600" : isCompleted ? "text-green-600" : "text-gray-500"
                  )}>
                    {status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
                    {isCurrent && <span className="ml-2 text-xs bg-blue-100 text-blue-600 px-2 py-0.5 rounded-full">Current</span>}
                  </h4>
                  <p className="text-sm text-gray-600 mt-1">
                    {statusDescriptions[status as keyof typeof statusDescriptions] ||
                     `Task is in ${status.replace('_', ' ')} status`}
                  </p>
                  {isCurrent && (
                    <p className="text-xs text-gray-500 mt-1">
                      Updated: {format(new Date(task.updated_at), 'PPP')}
                    </p>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

export default TaskTimeline;
