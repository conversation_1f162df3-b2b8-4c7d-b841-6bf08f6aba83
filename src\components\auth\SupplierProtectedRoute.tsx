import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import MainLayout from '@/components/layout/MainLayout';
import FriendlyAccessMessage from '@/components/ui/FriendlyAccessMessage';
import { Briefcase } from 'lucide-react';

interface SupplierProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
}

/**
 * A route component that only allows suppliers to access the content
 * Non-suppliers will see an access denied message
 */
const SupplierProtectedRoute: React.FC<SupplierProtectedRouteProps> = ({
  children,
  redirectTo = '/access-denied'
}) => {
  const { user, isSupplier, isLoading } = useAuth();

  // If auth is still loading, show nothing
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // If user is not logged in, redirect to login
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // If user is not a supplier, show friendly access message
  if (!isSupplier) {
    return (
      <MainLayout>
        <FriendlyAccessMessage
          title="Marketplace Information"
          message="The marketplace is where professional suppliers can view and bid on tasks that need external support."
          howItWorks="When schools post tasks that need external help, they appear here for qualified professionals to express interest and submit offers."
          additionalInfo="While you can't view the marketplace directly, any tasks from your school that need external support will be posted here for suppliers to find and respond to."
          icon={Briefcase}
          iconColorClass="text-blue-500"
          iconBgClass="bg-blue-50"
          primaryButtonText="Go to My Tasks"
          primaryButtonPath="/dashboard"
          secondaryButtonText="Learn About Supplier Accounts"
          secondaryButtonPath="/register"
        />
      </MainLayout>
    );
  }

  // User is a supplier, allow access
  return <>{children}</>;
};

export default SupplierProtectedRoute;
