export type Database = {
  public: {
    Tables: {
      organizations: {
        Row: {
          id: string;
          name: string;
          organization_type: 'school' | 'trust' | 'supplier';
          parent_organization_id: string | null;
          address?: string;
          city?: string;
          state?: string;
          zip?: string;
          phone?: string;
          website?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          organization_type: 'school' | 'trust' | 'supplier';
          parent_organization_id?: string | null;
          address?: string;
          city?: string;
          state?: string;
          zip?: string;
          phone?: string;
          website?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          organization_type?: 'school' | 'trust' | 'supplier';
          parent_organization_id?: string | null;
          address?: string;
          city?: string;
          state?: string;
          zip?: string;
          phone?: string;
          website?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      profiles: {
        Row: {
          id: string;
          user_id: string;
          organization_id: string;
          email: string;
          username?: string;
          avatar_url?: string;
          account_type: string;
          bio?: string;
          created_at: string;
          updated_at: string;
        };
      };
      user_invitations: {
        Row: {
          id: string;
          organization_id: string;
          email: string;
          role: string;
          token: string;
          status: 'pending' | 'accepted' | 'cancelled';
          created_at: string;
          updated_at: string;
        };
      };
      offers: {
        Row: {
          id: string;
          task_id: string;
          user_id: string;
          message: string;
          amount: number;
          status: string;
          created_at: string;
          updated_at: string;
        };
      };
      tasks: {
        Row: {
          id: string;
          title: string;
          description: string;
          status: string;
          created_at: string;
          updated_at: string;
          user_id: string;
        };
      };
      task_messages: {
        Row: {
          id: string;
          task_id: string;
          user_id: string;
          message: string;
          created_at: string;
        };
      };
    };
  };
};
