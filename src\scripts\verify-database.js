// <PERSON>ript to verify the database state after running the setup script
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function verifyDatabase() {
  try {
    console.log('Verifying database state...');

    // Check if organizations table exists and has data
    console.log('\n=== Organizations Table ===');
    const { data: orgs, error: orgsError } = await supabase
      .from('organizations')
      .select('*');

    if (orgsError) {
      console.error('Error fetching organizations:', orgsError);
    } else {
      console.log(`Found ${orgs.length} organizations:`);
      orgs.forEach((org, i) => {
        console.log(`${i+1}. ID: ${org.id}, Name: ${org.name}, Created: ${new Date(org.created_at).toLocaleString()}`);
      });
    }

    // Check if profiles table has the required columns
    console.log('\n=== Profiles Table Structure ===');
    let profileColumns = null;
    let columnsError = null;

    try {
      const result = await supabase.rpc('get_table_columns', { table_name: 'profiles' });
      profileColumns = result.data;
      columnsError = result.error;
    } catch (error) {
      columnsError = error;
    }

    if (columnsError) {
      console.log('Could not get columns via RPC, checking a sample profile instead');

      // Get a sample profile to check its structure
      const { data: sampleProfile, error: sampleError } = await supabase
        .from('profiles')
        .select('*')
        .limit(1)
        .single();

      if (sampleError) {
        console.error('Error fetching sample profile:', sampleError);
      } else {
        console.log('Profile columns:', Object.keys(sampleProfile));
        console.log('Has organization_id column:', Object.keys(sampleProfile).includes('organization_id'));
        console.log('Has role column:', Object.keys(sampleProfile).includes('role'));
      }
    } else {
      console.log('Profile columns:', profileColumns);
    }

    // Check the test user's profile
    console.log('\n=== Test User Profile ===');
    const { data: testProfile, error: testError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', 'e0eb9971-8690-4b51-b0d6-04805f2955ab')
      .single();

    if (testError) {
      console.error('Error fetching test user profile:', testError);
    } else {
      console.log('Test user profile:');
      console.log(`ID: ${testProfile.id}`);
      console.log(`First Name: ${testProfile.first_name || 'Not set'}`);
      console.log(`Last Name: ${testProfile.last_name || 'Not set'}`);
      console.log(`Organization ID: ${testProfile.organization_id || 'Not set'}`);
      console.log(`Role: ${testProfile.role || 'Not set'}`);

      // If the user has an organization, get its details
      if (testProfile.organization_id) {
        const { data: userOrg, error: userOrgError } = await supabase
          .from('organizations')
          .select('*')
          .eq('id', testProfile.organization_id)
          .single();

        if (userOrgError) {
          console.error('Error fetching user organization:', userOrgError);
        } else {
          console.log(`\nUser's Organization:`);
          console.log(`ID: ${userOrg.id}`);
          console.log(`Name: ${userOrg.name}`);
          console.log(`Created: ${new Date(userOrg.created_at).toLocaleString()}`);
        }
      }
    }

    // Check if the accept_invitation function exists
    console.log('\n=== Database Functions ===');
    try {
      const { data: funcResult, error: funcError } = await supabase
        .rpc('accept_invitation', { token_param: 'test', user_id_param: 'e0eb9971-8690-4b51-b0d6-04805f2955ab' });

      if (funcError) {
        if (funcError.message.includes('relation "public.user_invitations" does not exist')) {
          console.log('The accept_invitation function exists but requires the user_invitations table');
        } else {
          console.log('Error testing accept_invitation function:', funcError.message);
        }
      } else {
        console.log('The accept_invitation function exists and is callable');
      }
    } catch (error) {
      console.log('Error testing accept_invitation function:', error.message);
    }

    console.log('\nVerification complete');
  } catch (error) {
    console.error('Error verifying database:', error);
  }
}

verifyDatabase();
