import { supabase } from '@/integrations/supabase/client';

/**
 * Test the Supabase connection by making a simple query
 */
export const testSupabaseConnection = async () => {
  console.log('Testing Supabase connection...');
  
  try {
    // First, check if we can connect to Supabase
    const { data: healthData, error: healthError } = await supabase.from('tasks').select('count()', { count: 'exact' });
    
    if (healthError) {
      console.error('Supabase connection error:', healthError);
      return {
        success: false,
        message: `Connection failed: ${healthError.message}`,
        error: healthError
      };
    }
    
    console.log('Supabase connection successful!', healthData);
    
    // Try to fetch a small amount of data to verify read access
    const { data: tasksData, error: tasksError } = await supabase
      .from('tasks')
      .select('id, title')
      .limit(3);
    
    if (tasksError) {
      console.error('Error fetching tasks:', tasksError);
      return {
        success: false,
        message: `Connection successful but query failed: ${tasksError.message}`,
        error: tasksError
      };
    }
    
    return {
      success: true,
      message: 'Supabase connection and query successful!',
      data: {
        count: healthData[0]?.count,
        sampleTasks: tasksData
      }
    };
  } catch (error) {
    console.error('Unexpected error testing Supabase connection:', error);
    return {
      success: false,
      message: `Unexpected error: ${error.message}`,
      error
    };
  }
};

// Export a function to test authentication
export const testSupabaseAuth = async () => {
  console.log('Testing Supabase authentication...');
  
  try {
    // Check if we have a current session
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('Error getting session:', sessionError);
      return {
        success: false,
        message: `Auth check failed: ${sessionError.message}`,
        error: sessionError
      };
    }
    
    const isAuthenticated = !!sessionData?.session;
    
    return {
      success: true,
      message: isAuthenticated 
        ? 'User is authenticated' 
        : 'No active session (not authenticated)',
      data: {
        isAuthenticated,
        session: sessionData?.session ? {
          user: sessionData.session.user,
          expires_at: sessionData.session.expires_at
        } : null
      }
    };
  } catch (error) {
    console.error('Unexpected error testing Supabase auth:', error);
    return {
      success: false,
      message: `Unexpected error: ${error.message}`,
      error
    };
  }
};
