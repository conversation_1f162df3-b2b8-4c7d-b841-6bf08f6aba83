// This is a patch file to fix the issue where admins can't see the task completion component
// Apply this change to src/pages/FixedTask.tsx

// Find this section in the file (around line 486-510):
/*
  {/* Task Completion Actions - Only shown to task owners for assigned tasks */}
  {isTaskOwner && (
    <>
      {/* Debug info */}
      {console.log('Task Completion Debug:', {
        isTaskOwner,
        taskStatus: task?.status,
        hasAcceptedOffer: !!offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted'),
        acceptedOffer: offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted'),
        offers: offers?.map(o => ({ id: o.id, status: o.status, normalizedStatus: normalizeStatus(o.status) }))
      })}

      <TaskCompletionActions
        task={task}
        acceptedOffer={offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted') || null}
        onTaskUpdated={() => queryClient.invalidateQueries({ queryKey: ['task', id] })}
      />

      <TaskPaymentActions
        task={task}
        acceptedOffer={offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted') || null}
        onTaskUpdated={() => queryClient.invalidateQueries({ queryKey: ['task', id] })}
      />
    </>
  )}
*/

// Replace it with this code that allows both task owners and admins to see the components:
/*
  {/* Task Completion Actions - Shown to task owners and admins for assigned tasks */}
  {(isTaskOwner || isAdmin) && (
    <>
      {/* Debug info */}
      {console.log('Task Completion Debug:', {
        isTaskOwner,
        isAdmin,
        canManageCompletion: isTaskOwner || isAdmin,
        taskStatus: task?.status,
        hasAcceptedOffer: !!offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted'),
        acceptedOffer: offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted'),
        offers: offers?.map(o => ({ id: o.id, status: o.status, normalizedStatus: normalizeStatus(o.status) }))
      })}

      <TaskCompletionActions
        task={task}
        acceptedOffer={offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted') || null}
        onTaskUpdated={() => queryClient.invalidateQueries({ queryKey: ['task', id] })}
      />

      <TaskPaymentActions
        task={task}
        acceptedOffer={offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted') || null}
        onTaskUpdated={() => queryClient.invalidateQueries({ queryKey: ['task', id] })}
      />
    </>
  )}
*/