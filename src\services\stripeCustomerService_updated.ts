import { createClient } from '@supabase/supabase-js';
import Stripe from 'stripe';

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Initialize Stripe
const stripeSecretKey = import.meta.env.VITE_STRIPE_SECRET_KEY;
const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2023-10-16',
});

/**
 * Service for managing Stripe customers
 */
export const stripeCustomerService = {
  /**
   * Get or create a Stripe customer for a user
   * @param userId The user ID
   * @param email The user's email
   * @param name The user's name
   * @returns The Stripe customer ID
   */
  async getOrCreateCustomer(userId: string, email: string | string[], name?: string): Promise<string> {
    try {
      // Check if the user already has a Stripe customer
      const { data: stripeCustomers, error: stripeCustomersError } = await supabase
        .from('stripe_customers')
        .select('customer_id')
        .eq('user_id', userId)
        .limit(1);

      if (stripeCustomersError) {
        console.error('Error fetching Stripe customer:', stripeCustomersError);

        // If the stripe_customers table doesn't exist, create a new Stripe customer without storing it
        if (stripeCustomersError.code === '42P01') {
          console.warn('stripe_customers table does not exist. Creating a new Stripe customer without storing it.');
          // Handle email as string or array
          const customerEmail = Array.isArray(email) ? email[0] : email;
          const customer = await stripe.customers.create({
            email: customerEmail,
            name: name || customerEmail,
            metadata: {
              user_id: userId
            }
          });

          return customer.id;
        }

        throw stripeCustomersError;
      }

      // If the user already has a Stripe customer, return it
      if (stripeCustomers && stripeCustomers.length > 0) {
        const stripeCustomerId = stripeCustomers[0].customer_id;

        // Verify that the customer exists in Stripe
        try {
          await stripe.customers.retrieve(stripeCustomerId);
          return stripeCustomerId;
        } catch (error) {
          console.error('Error retrieving Stripe customer:', error);
          console.log('Creating a new Stripe customer...');
        }
      }

      // Create a new Stripe customer
      const customer = await stripe.customers.create({
        email,
        name: name || email,
        metadata: {
          user_id: userId
        }
      });

      // Store the Stripe customer in the database
      try {
        await supabase
          .from('stripe_customers')
          .insert({
            user_id: userId,
            customer_id: customer.id,
          });
      } catch (error) {
        console.error('Error storing Stripe customer:', error);
        console.warn('Could not store Stripe customer in the database. The stripe_customers table might not exist.');
      }

      return customer.id;
    } catch (error) {
      console.error('Error in getOrCreateCustomer:', error);
      throw error;
    }
  },

  /**
   * Get a user's Stripe customer ID
   * @param userId The user ID
   * @returns The Stripe customer ID, or null if not found
   */
  async getCustomerId(userId: string): Promise<string | null> {
    try {
      // Check if the user already has a Stripe customer
      const { data: stripeCustomers, error: stripeCustomersError } = await supabase
        .from('stripe_customers')
        .select('customer_id')
        .eq('user_id', userId)
        .limit(1);

      if (stripeCustomersError) {
        console.error('Error fetching Stripe customer:', stripeCustomersError);

        // If the stripe_customers table doesn't exist, return null
        if (stripeCustomersError.code === '42P01') {
          console.warn('stripe_customers table does not exist.');
          return null;
        }

        throw stripeCustomersError;
      }

      // If the user already has a Stripe customer, return it
      if (stripeCustomers && stripeCustomers.length > 0) {
        return stripeCustomers[0].customer_id;
      }

      return null;
    } catch (error) {
      console.error('Error in getCustomerId:', error);
      throw error;
    }
  },

  /**
   * Update a user's Stripe customer
   * @param userId The user ID
   * @param data The data to update
   * @returns The updated Stripe customer
   */
  async updateCustomer(userId: string, data: Stripe.CustomerUpdateParams): Promise<Stripe.Customer> {
    try {
      // Get the user's Stripe customer ID
      const customerId = await this.getCustomerId(userId);

      if (!customerId) {
        throw new Error(`No Stripe customer found for user ${userId}`);
      }

      // Update the Stripe customer
      const customer = await stripe.customers.update(customerId, data);

      return customer;
    } catch (error) {
      console.error('Error in updateCustomer:', error);
      throw error;
    }
  },

  /**
   * Delete a user's Stripe customer
   * @param userId The user ID
   * @returns True if the customer was deleted, false otherwise
   */
  async deleteCustomer(userId: string): Promise<boolean> {
    try {
      // Get the user's Stripe customer ID
      const customerId = await this.getCustomerId(userId);

      if (!customerId) {
        return false;
      }

      // Delete the Stripe customer
      await stripe.customers.del(customerId);

      // Delete the Stripe customer from the database
      try {
        await supabase
          .from('stripe_customers')
          .delete()
          .eq('user_id', userId);
      } catch (error) {
        console.error('Error deleting Stripe customer from database:', error);
        console.warn('Could not delete Stripe customer from the database. The stripe_customers table might not exist.');
      }

      return true;
    } catch (error) {
      console.error('Error in deleteCustomer:', error);
      throw error;
    }
  }
};

export default stripeCustomerService;
