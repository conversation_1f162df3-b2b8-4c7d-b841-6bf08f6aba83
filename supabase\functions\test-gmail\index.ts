// Dynamic Email Sender Edge Function
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

// Define CORS headers directly
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type"
};

// Environment variables
const RESEND_API_KEY = Deno.env.get("RESEND_API_KEY");

// Interface for the email payload
interface EmailPayload {
  to: string;
  subject: string;
  html_content?: string;
  text_content?: string;
  from?: string;
}

// Main handler function
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    console.log("Processing dynamic email request...");

    // Check if Resend API key is set
    if (!RESEND_API_KEY) {
      return new Response(
        JSON.stringify({ error: "RESEND_API_KEY environment variable is not set" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    // Get the request body
    const payload: EmailPayload = await req.json();

    // Validate required fields
    if (!payload.to || !payload.subject) {
      return new Response(
        JSON.stringify({ error: "Missing required fields: to and subject are required" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    // Use provided HTML content or generate a default one
    const htmlContent = payload.html_content || `
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          h1 { color: #2563eb; margin-bottom: 20px; }
          .message-box { margin-top: 20px; padding: 15px; background-color: #f9fafb; border-radius: 5px; border: 1px solid #e5e7eb; }
          .footer { margin-top: 30px; font-size: 0.8em; color: #666; border-top: 1px solid #eee; padding-top: 15px; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>${payload.subject}</h1>

          <div class="message-box">
            <p>This is a default email message.</p>
            <p>Timestamp: ${new Date().toISOString()}</p>
          </div>

          <div class="footer">
            <p>This is an automated message from ClassTasker. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    // Create plain text version by stripping HTML tags if not provided
    const plainText = payload.text_content || htmlContent.replace(/<[^>]*>/g, '');

    console.log("Sending email via Resend API...");

    // Prepare the request to Resend API
    const data = {
      from: payload.from || "ClassTasker Support <<EMAIL>>",
      to: [payload.to],
      subject: payload.subject,
      html: htmlContent,
      text: plainText
    };

    // Send the request to Resend API
    const response = await fetch("https://api.resend.com/emails", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${RESEND_API_KEY}`,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(data)
    });

    // Log the response status
    console.log(`Resend API response status: ${response.status}`);

    // Get the response text
    const responseText = await response.text();
    console.log(`Resend API response: ${responseText}`);

    // Check if the request was successful
    if (!response.ok) {
      return new Response(
        JSON.stringify({
          error: "Failed to send email via Resend API",
          details: responseText
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    // Try to parse the response as JSON
    let result;
    try {
      result = JSON.parse(responseText);
      console.log(`Email sent successfully to ${payload.to} using Resend. Message ID: ${result.id}`);
    } catch (parseError) {
      console.log(`Email sent successfully to ${payload.to} using Resend, but couldn't parse response: ${responseText}`);
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: `Email sent successfully to ${payload.to}`,
        data: result
      }),
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  } catch (error) {
    console.error("Error processing request:", error);

    return new Response(
      JSON.stringify({
        error: "Failed to process request",
        details: error.message || "Unknown error"
      }),
      {
        status: 500,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        }
      }
    );
  }
});
