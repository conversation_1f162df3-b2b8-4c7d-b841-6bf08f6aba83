/**
 * Cleanup Unauthorized Chat Access
 *
 * This script removes users from GetStream channels they should not have access to
 * based on organization membership and task visibility.
 */

import { createClient } from '@supabase/supabase-js';
import { StreamChat } from 'stream-chat';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL!;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY!;
const getstreamApiKey = process.env.GETSTREAM_API_KEY || process.env.VITE_GETSTREAM_API_KEY!;
const getstreamApiSecret = process.env.GETSTREAM_API_SECRET!;

console.log('🧹 Starting unauthorized chat cleanup...');
console.log('Environment variables check:');
console.log('VITE_SUPABASE_URL:', supabaseUrl ? 'Set' : 'Missing');
console.log('VITE_SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? 'Set' : 'Missing');
console.log('GETSTREAM_API_KEY:', getstreamApiKey ? 'Set' : 'Missing');
console.log('GETSTREAM_API_SECRET:', getstreamApiSecret ? 'Set' : 'Missing');

if (!supabaseUrl || !supabaseServiceKey || !getstreamApiKey || !getstreamApiSecret) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);
const streamClient = StreamChat.getInstance(getstreamApiKey, getstreamApiSecret);

interface CleanupResult {
  channelId: string;
  taskId: string;
  removedUsers: string[];
  errors: string[];
}

async function cleanupUnauthorizedChatAccess() {
  const results: CleanupResult[] = [];

  try {
    console.log('📋 Fetching all task-related channels...');

    // Get all messaging channels and filter for task channels
    const allChannels = await streamClient.queryChannels({
      type: 'messaging'
    });

    // Filter for channels that start with 'task-'
    const channels = allChannels.filter(channel => channel.id.startsWith('task-'));

    console.log(`Found ${channels.length} task-related channels`);

    for (const channel of channels) {
      const channelId = channel.id;
      const taskId = channelId.replace('task-', '');

      console.log(`\n🔍 Processing channel: ${channelId} (Task: ${taskId})`);

      const result: CleanupResult = {
        channelId,
        taskId,
        removedUsers: [],
        errors: []
      };

      try {
        // Get task details
        const { data: task, error: taskError } = await supabase
          .from('tasks')
          .select('id, organization_id, visibility, title')
          .eq('id', taskId)
          .single();

        if (taskError || !task) {
          result.errors.push(`Task not found: ${taskError?.message || 'Unknown error'}`);
          results.push(result);
          continue;
        }

        console.log(`   Task: "${task.title}" (Org: ${task.organization_id}, Visibility: ${task.visibility})`);

        // Get channel members
        const channelState = await channel.query();
        const members = Object.keys(channelState.members || {});

        console.log(`   Channel has ${members.length} members`);

        // Check each member's access rights
        for (const memberId of members) {
          try {
            // Get member's profile
            const { data: memberProfile, error: profileError } = await supabase
              .from('profiles')
              .select('id, organization_id, account_type, email')
              .eq('id', memberId)
              .single();

            if (profileError || !memberProfile) {
              console.log(`   ⚠️  Member ${memberId} profile not found, removing from channel`);
              await channel.removeMembers([memberId]);
              result.removedUsers.push(`${memberId} (profile not found)`);
              continue;
            }

            // Check if member should have access
            const hasAccess = (
              // Same organization
              memberProfile.organization_id === task.organization_id ||
              // Supplier with public task
              (memberProfile.account_type === 'supplier' && task.visibility === 'public')
            );

            if (!hasAccess) {
              console.log(`   ❌ Removing unauthorized member: ${memberProfile.email[0]} (Org: ${memberProfile.organization_id})`);
              await channel.removeMembers([memberId]);
              result.removedUsers.push(`${memberProfile.email[0]} (${memberId})`);
            } else {
              console.log(`   ✅ Member authorized: ${memberProfile.email[0]}`);
            }

          } catch (memberError) {
            const errorMsg = `Error processing member ${memberId}: ${memberError.message}`;
            console.error(`   ❌ ${errorMsg}`);
            result.errors.push(errorMsg);
          }
        }

      } catch (channelError) {
        const errorMsg = `Error processing channel ${channelId}: ${channelError.message}`;
        console.error(`   ❌ ${errorMsg}`);
        result.errors.push(errorMsg);
      }

      results.push(result);
    }

    // Print summary
    console.log('\n📊 Cleanup Summary:');
    console.log('=' .repeat(50));

    let totalRemoved = 0;
    let totalErrors = 0;

    results.forEach(result => {
      if (result.removedUsers.length > 0 || result.errors.length > 0) {
        console.log(`\nChannel: ${result.channelId}`);

        if (result.removedUsers.length > 0) {
          console.log(`  Removed users (${result.removedUsers.length}):`);
          result.removedUsers.forEach(user => console.log(`    - ${user}`));
          totalRemoved += result.removedUsers.length;
        }

        if (result.errors.length > 0) {
          console.log(`  Errors (${result.errors.length}):`);
          result.errors.forEach(error => console.log(`    - ${error}`));
          totalErrors += result.errors.length;
        }
      }
    });

    console.log(`\n📈 Total Results:`);
    console.log(`   Channels processed: ${results.length}`);
    console.log(`   Users removed: ${totalRemoved}`);
    console.log(`   Errors encountered: ${totalErrors}`);

    if (totalRemoved > 0) {
      console.log('\n🎉 Unauthorized chat access has been cleaned up!');
    } else {
      console.log('\n✅ No unauthorized access found - all channels are secure!');
    }

  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    throw error;
  }
}

// Run the cleanup
cleanupUnauthorizedChatAccess()
  .then(() => {
    console.log('\n✅ Cleanup completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Cleanup failed:', error);
    process.exit(1);
  });
