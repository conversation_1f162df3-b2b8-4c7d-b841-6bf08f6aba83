/**
 * Utility functions for chat-related functionality
 */

import { Task, isInternalTask, isExternalTask } from '@/types/tasks';

/**
 * Determines if a chat should be shown for a task based on its properties
 * @param task The task to check
 * @returns Whether a chat should be shown for this task
 */
export function shouldShowChatForTask(task: any): boolean {
  if (!task) return false;

  // For typed tasks
  if (task.type) {
    // Internal tasks should show chat if they're assigned
    if (task.type === 'internal') {
      return !!task.assigned_to;
    }
    
    // External tasks should show chat if they're public and not in 'open' status
    if (task.type === 'external') {
      return task.visibility === 'public' && task.status !== 'open';
    }
  } 
  // For legacy tasks (backward compatibility)
  else {
    // Internal tasks should show chat if they're assigned
    if (task.visibility === 'internal') {
      return !!task.assigned_to;
    }
    
    // External tasks should show chat if they're public and not in 'open' status
    if (task.visibility === 'public') {
      return task.status !== 'open';
    }
  }
  
  return false;
}
