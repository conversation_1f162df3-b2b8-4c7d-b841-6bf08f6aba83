// Import required dependencies
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { SmtpClient } from "https://deno.land/x/smtp@v0.7.0/mod.ts";

interface EmailConfig {
  provider: string;
  fromEmail: string;
  fromName?: string;



  // SMTP specific
  smtpHost?: string;
  smtpPort?: number;
  smtpUsername?: string;
  smtpPassword?: string;
  smtpSecure?: boolean;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  }

  // Only allow POST requests
  if (req.method !== "POST") {
    return new Response(JSON.stringify({ error: "Method not allowed" }), {
      status: 405,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
    });
  }

  try {
    // Parse the request body
    const { config, testEmail } = await req.json();

    // Validate the request
    if (!config || !testEmail) {
      return new Response(JSON.stringify({ error: "Missing required parameters" }), {
        status: 400,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      });
    }

    // Validate email configuration
    if (config.provider === "smtp") {
      if (!config.smtpHost || !config.smtpPort || !config.smtpUsername || !config.smtpPassword) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Missing SMTP configuration. Please provide host, port, username, and password.",
          }),
          {
            status: 400,
            headers: {
              "Content-Type": "application/json",
              "Access-Control-Allow-Origin": "*",
            },
          }
        );
      }

    } else if (config.provider === "none") {
      // Debug mode - just log the email and return success
      console.log("Debug mode - would have sent test email to:", testEmail);

      return new Response(
        JSON.stringify({
          success: true,
          message: `Test email would be sent to ${testEmail} (DEBUG MODE)`,
        }),
        {
          status: 200,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
    } else {
      return new Response(
        JSON.stringify({
          success: false,
          message: `Unsupported email provider: ${config.provider}`,
        }),
        {
          status: 400,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
    }

    // Log the test email request
    console.log("Testing email configuration:", {
      provider: config.provider,
      from: `${config.fromName || "Classtasker"} <${config.fromEmail}>`,
      to: testEmail,
      smtpHost: config.smtpHost,
      smtpPort: config.smtpPort,
    });

    if (config.provider === "smtp") {
      // Create SMTP client
      const client = new SmtpClient();

      try {
        // Connect to SMTP server based on secure flag
        if (config.smtpSecure) {
          await client.connectTLS({
            hostname: config.smtpHost!,
            port: config.smtpPort!,
            username: config.smtpUsername!,
            password: config.smtpPassword!,
          });
        } else {
          await client.connect({
            hostname: config.smtpHost!,
            port: config.smtpPort!,
          });

          // Use STARTTLS if available
          try {
            await client.starttls();
          } catch (starttlsError) {
            console.log("STARTTLS not supported or failed, continuing with unencrypted connection");
          }

          // Authenticate after connection
          await client.login({
            username: config.smtpUsername!,
            password: config.smtpPassword!,
          });
        }

      // Create email content
      const timestamp = new Date().toISOString();
      const subject = `Classtasker Email Test - ${timestamp}`;
      const body = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Classtasker Email Test</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #4f46e5; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
            .content { padding: 20px; border: 1px solid #e0e0e0; border-top: none; border-radius: 0 0 5px 5px; }
            .footer { margin-top: 20px; font-size: 12px; color: #666; text-align: center; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Classtasker Email Test</h1>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>This is a test email from Classtasker to verify your email configuration.</p>
            <p>If you're seeing this, your email configuration is working correctly!</p>
            <p><strong>Test timestamp:</strong> ${timestamp}</p>
            <p>Configuration details:</p>
            <ul>
              <li>Provider: ${config.provider}</li>
              <li>From: ${config.fromEmail}</li>
              <li>SMTP Host: ${config.smtpHost}</li>
              <li>SMTP Port: ${config.smtpPort}</li>
              <li>Secure: ${config.smtpSecure ? 'Yes' : 'No'}</li>
              <li>To: ${testEmail}</li>
            </ul>
          </div>
          <div class="footer">
            <p>This is an automated message from Classtasker. Please do not reply to this email.</p>
            <p>&copy; ${new Date().getFullYear()} Classtasker. All rights reserved.</p>
          </div>
        </body>
        </html>
      `;

      // Create plain text version by stripping HTML tags
      const plainText = body.replace(/<[^>]*>/g, '');

      // Send test email
      await client.send({
        from: `${config.fromName || "Classtasker"} <${config.fromEmail}>`,
        to: testEmail,
        subject: subject,
        content: plainText,
        html: body,
      });

      // Close connection
      await client.close();

      // Return success response
      return new Response(
        JSON.stringify({
          success: true,
          message: `Test email sent to ${testEmail} using ${config.provider} provider.`,
        }),
        {
          status: 200,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
    } catch (emailError) {
      // Close connection if it's open
      try {
        await client.close();
      } catch (closeError) {
        console.error("Error closing SMTP connection:", closeError);
      }

      console.error("Error sending test email:", emailError);
      return new Response(
        JSON.stringify({
          success: false,
          message: `Error sending test email: ${emailError.message}`,
        }),
        {
          status: 500,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
    }

  } catch (error) {
    console.error("Error processing test email request:", error);

    // Return error response
    return new Response(
      JSON.stringify({
        success: false,
        message: `Server error: ${error.message}`,
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      }
    );
  }
});
