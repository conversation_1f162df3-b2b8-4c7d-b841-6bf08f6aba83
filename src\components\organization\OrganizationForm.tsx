import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Organization } from '@/types/organization';
import LocationSearch from '@/components/ui/location-search';
import { getCoordinates } from '@/utils/location-utils';

const organizationSchema = z.object({
  name: z.string().min(2, { message: 'Organization name must be at least 2 characters' }),
  organization_type: z.enum(['school', 'trust']),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zip: z.string().optional(),
  phone: z.string().optional(),
  website: z.string().url({ message: 'Please enter a valid URL' }).optional().or(z.literal('')),
  // New location fields
  location_formatted: z.string().optional(),
  location_lat: z.number().optional(),
  location_lng: z.number().optional(),
  location_place_id: z.string().optional(),
});

type OrganizationFormValues = z.infer<typeof organizationSchema>;

interface OrganizationFormProps {
  initialData?: Partial<Organization>;
  onSubmit: (data: OrganizationFormValues) => Promise<void>;
  isLoading: boolean;
}

export function OrganizationForm({ initialData, onSubmit, isLoading }: OrganizationFormProps) {
  const [locationInput, setLocationInput] = useState('');
  const [isGeocodingLocation, setIsGeocodingLocation] = useState(false);

  const form = useForm<OrganizationFormValues>({
    resolver: zodResolver(organizationSchema),
    defaultValues: {
      name: initialData?.name || '',
      organization_type: (initialData as any)?.organization_type || 'school',
      address: initialData?.address || '',
      city: initialData?.city || '',
      state: initialData?.state || '',
      zip: initialData?.zip || '',
      phone: initialData?.phone || '',
      website: initialData?.website || '',
      // Initialize location fields
      location_formatted: (initialData as any)?.location_formatted || '',
      location_lat: (initialData as any)?.location_lat || undefined,
      location_lng: (initialData as any)?.location_lng || undefined,
      location_place_id: (initialData as any)?.location_place_id || '',
    },
  });

  // Initialize location input from formatted address or address field
  useEffect(() => {
    const formattedAddress = (initialData as any)?.location_formatted;
    const address = initialData?.address;

    if (formattedAddress) {
      setLocationInput(formattedAddress);
    } else if (address) {
      // Combine address fields to create a full address string
      const addressParts = [
        address,
        initialData?.city,
        initialData?.state,
        initialData?.zip
      ].filter(Boolean);

      setLocationInput(addressParts.join(', '));
    }
  }, [initialData]);

  // Handle location selection
  const handleLocationChange = async (location: string) => {
    setLocationInput(location);

    if (location) {
      setIsGeocodingLocation(true);
      try {
        // Get coordinates for the selected location
        const coordinates = await getCoordinates(location);

        if (coordinates) {
          // Update form values with location data
          form.setValue('location_formatted', location);
          form.setValue('location_lat', coordinates.lat);
          form.setValue('location_lng', coordinates.lng);

          // Also update the address field for backward compatibility
          form.setValue('address', location);
        }
      } catch (error) {
        console.error('Error geocoding location:', error);
      } finally {
        setIsGeocodingLocation(false);
      }
    }
  };

  const handleSubmit = async (data: OrganizationFormValues) => {
    await onSubmit(data);
  };

  return (
    <Card>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)}>
          <CardContent className="space-y-4 pt-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Organization Name*</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter organization name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="organization_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Organization Type*</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select organization type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="school">Individual School</SelectItem>
                      <SelectItem value="trust">Multi-Academy Trust</SelectItem>
                      <SelectItem value="supplier">Supplier Business</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                  <p className="text-sm text-gray-500 mt-1">
                    Select the type of organization you're creating: an individual school, a Multi-Academy Trust, or a supplier business.
                  </p>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="location_formatted"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Location</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <LocationSearch
                        value={locationInput}
                        onChange={handleLocationChange}
                        placeholder="Enter organization address"
                        label=""
                        className=""
                      />
                      {isGeocodingLocation && (
                        <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                          <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-opacity-50 border-t-transparent rounded-full"></div>
                        </div>
                      )}
                    </div>
                  </FormControl>
                  <FormMessage />
                  <p className="text-sm text-gray-500 mt-1">
                    Enter your organization's full address to enable location-based features
                  </p>
                </FormItem>
              )}
            />

            {/* Hidden fields to store location data */}
            <input type="hidden" {...form.register('location_lat')} />
            <input type="hidden" {...form.register('location_lng')} />
            <input type="hidden" {...form.register('location_place_id')} />

            {/* Keep the address field for backward compatibility but hide it */}
            <input type="hidden" {...form.register('address')} />
            <input type="hidden" {...form.register('city')} />
            <input type="hidden" {...form.register('state')} />
            <input type="hidden" {...form.register('zip')} />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone Number</FormLabel>
                  <FormControl>
                    <Input placeholder="Phone number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="website"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Website</FormLabel>
                  <FormControl>
                    <Input placeholder="https://www.example.com" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
          <CardFooter className="flex justify-end space-x-2 border-t px-6 py-4">
            <Button variant="outline" type="button" disabled={isLoading}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : initialData?.id ? 'Update Organization' : 'Create Organization'}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}

export default OrganizationForm;
