/**
 * GetStream Token Server
 *
 * A simple Express server for generating GetStream tokens and managing channels.
 * Run with: node server/getstream-token-server-fixed.js
 */

import express from 'express';
import cors from 'cors';
import { StreamChat } from 'stream-chat';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get API key and secret from environment variables
const apiKey = process.env.GETSTREAM_API_KEY;
const apiSecret = process.env.GETSTREAM_API_SECRET;

// Check if API key and secret are available
if (!apiKey || !apiSecret) {
  console.error('Error: GetStream API key or secret is missing in environment variables.');
  console.error('Make sure GETSTREAM_API_KEY and GETSTREAM_API_SECRET are set in your .env file.');
  process.exit(1);
}

// Create a server-side client for GetStream
const serverClient = StreamChat.getInstance(apiKey, apiSecret);

// Create an Express app
const app = express();
const PORT = 3002;

// Enable CORS for all routes
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));

// Add OPTIONS handling for preflight requests
app.options('/*', cors());

// Parse JSON request bodies
app.use(express.json());

// Generate a token for a user (old endpoint)
app.post('/token', async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    console.log('Generating token for user:', userId);

    // Generate a token for the user
    const token = serverClient.createToken(userId);

    console.log('Token generated successfully');

    res.json({ token });
  } catch (error) {
    console.error('Error generating token:', error);
    res.status(500).json({ error: 'Failed to generate token' });
  }
});

// API Routes for Vercel Deployment

// Generate a token for a user (new endpoint)
app.post('/api/getstream/token', async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    console.log('Generating token for user (new endpoint):', userId);

    // Generate a token for the user
    const token = serverClient.createToken(userId);

    console.log('Token generated successfully');

    res.json({ token });
  } catch (error) {
    console.error('Error generating token:', error);
    res.status(500).json({ error: 'Failed to generate token' });
  }
});

// Create a channel for a task (new endpoint)
app.post('/api/getstream/channels', async (req, res) => {
  try {
    const { taskId, taskTitle, members } = req.body;

    if (!taskId || !taskTitle) {
      return res.status(400).json({ error: 'Task ID and title are required' });
    }

    console.log('Creating channel for task (new endpoint):', taskId);

    // Create a channel
    const channelId = `task-${taskId}`;

    // Use provided members or create an empty array
    const channelMembers = members && Array.isArray(members) ? members : [];

    // Create users if they don't exist
    if (channelMembers.length > 0) {
      console.log('Creating users if they don\'t exist:', channelMembers);

      for (const userId of channelMembers) {
        try {
          // Check if user exists
          await serverClient.queryUsers({ id: userId });
          console.log(`User ${userId} already exists`);
        } catch (userError) {
          // Create the user if they don't exist
          try {
            await serverClient.upsertUser({
              id: userId,
              name: `User ${userId}`,
              role: 'user'
            });
            console.log(`Created user ${userId}`);
          } catch (createError) {
            console.error(`Failed to create user ${userId}:`, createError);
          }
        }
      }
    }

    try {
      // First try to get the channel if it exists
      const channel = serverClient.channel('messaging', channelId);
      await channel.query();

      // If channel exists, update members
      if (channelMembers.length > 0) {
        // Add members to the channel
        await channel.addMembers(channelMembers);
        console.log(`Added ${channelMembers.length} members to existing channel:`, channelId);
      }

      console.log('Channel already exists:', channelId);

      res.json({
        channelId,
        channel: channel.id,
        members: channel.state.members,
        status: 'updated'
      });
    } catch (error) {
      // If channel doesn't exist, create it
      console.log('Channel does not exist, creating new channel');

      // Create a new channel with created_by_id parameter
      const channel = serverClient.channel('messaging', channelId, {
        name: taskTitle,
        members: channelMembers,
        task_id: taskId,
        created_by_id: 'system' // Adding the required created_by_id parameter
      });

      await channel.create();

      console.log('Channel created successfully:', channelId);

      res.json({
        channelId,
        channel: channel.id,
        members: channel.state.members,
        status: 'created'
      });
    }
  } catch (error) {
    console.error('Error creating/updating channel:', error);
    res.status(500).json({ error: 'Failed to create/update channel' });
  }
});

// Send a system message to a channel (new endpoint)
app.post('/api/getstream/system-message', async (req, res) => {
  try {
    const { channelId, text } = req.body;

    if (!channelId || !text) {
      return res.status(400).json({ error: 'Channel ID and text are required' });
    }

    console.log('Sending system message to channel (new endpoint):', channelId);

    // Get the channel
    const channel = serverClient.channel('messaging', channelId);

    // Send the system message
    const message = await channel.sendMessage({
      text,
      type: 'system',
      user_id: 'system',
    });

    console.log('System message sent successfully:', message);

    res.json({ success: true, message });
  } catch (error) {
    console.error('Error sending system message:', error);
    res.status(500).json({ error: 'Failed to send system message' });
  }
});

// Create a user (new endpoint)
app.post('/api/getstream/create-user', async (req, res) => {
  try {
    const { userId, userName } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    console.log('Creating user (new endpoint):', userId);

    try {
      // Create the user
      const user = await serverClient.upsertUser({
        id: userId,
        name: userName || userId,
        role: 'user'
      });

      console.log('User created successfully:', userId);

      res.json({ success: true, user });
    } catch (error) {
      console.error('Error creating user:', error);
      res.status(500).json({ error: 'Failed to create user' });
    }
  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({ error: 'Failed to create user' });
  }
});

// Simple route for testing
app.get('/', (req, res) => {
  res.json({ status: 'GetStream Token Server is running' });
});

// Start the server
app.listen(PORT, () => {
  console.log(`GetStream Token Server listening on port ${PORT}`);
  console.log(`API Key: ${apiKey}`);
  console.log(`API Secret: ${apiSecret.substring(0, 5)}...${apiSecret.substring(apiSecret.length - 5)}`);
});
