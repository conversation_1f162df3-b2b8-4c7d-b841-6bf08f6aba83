import { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import './payment-processor.css'; // Import custom styles
import {
  Elements,
  PaymentElement,
  useStripe,
  useElements,
  AddressElement,
  LinkAuthenticationElement
} from '@stripe/react-stripe-js';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, AlertCircle, CheckCircle2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { stripeService } from '@/services/stripeService';
import { supabase } from '@/integrations/supabase/client';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Initialize Stripe
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

// Define appearance options for Stripe Elements
const appearance = {
  theme: 'stripe',
  variables: {
    colorPrimary: '#0570de',
    colorBackground: '#ffffff',
    colorText: '#30313d',
    colorDanger: '#df1b41',
    fontFamily: 'system-ui, sans-serif',
    spacingUnit: '4px',
    borderRadius: '8px',
    fontSizeBase: '15px',
  },
  rules: {
    '.Tab': {
      border: '1px solid #e6e6e6',
      boxShadow: '0px 1px 1px rgba(0, 0, 0, 0.03)',
      marginBottom: '8px',
      padding: '8px 12px',
    },
    '.Tab:hover': {
      color: 'var(--colorText)',
      backgroundColor: '#f9f9f9',
    },
    '.Tab--selected': {
      borderColor: '#0570de',
      boxShadow: '0px 1px 1px rgba(0, 0, 0, 0.03)',
      backgroundColor: '#f6f8fa',
    },
    '.Input--invalid': {
      boxShadow: '0 1px 1px 0 rgba(0, 0, 0, 0.07), 0 0 0 2px var(--colorDanger)',
    },
    '.Label': {
      fontSize: '14px',
      fontWeight: '500',
      marginBottom: '6px',
    },
    '.TabLabel': {
      fontSize: '15px',
    },
    // Make form elements more compact
  },
};

// Check if we're on a production domain with HTTPS
const isProduction = window.location.protocol === 'https:' && !window.location.hostname.includes('localhost');

// Define payment method order based on environment
const paymentMethodOrder = [
  'card',
  'paypal',
  'revolut_pay',
  'bacs_debit',
];

// Only include Apple Pay and Google Pay on production domains with HTTPS
if (isProduction) {
  paymentMethodOrder.push('apple_pay');
  paymentMethodOrder.push('google_pay');
}

// Main payment processor component
export default function EnhancedPaymentProcessor({
  taskId,
  offerId,
  amount,
  onSuccess,
  onCancel
}: {
  taskId: string;
  offerId: string;
  amount: number;
  onSuccess?: () => void;
  onCancel?: () => void;
}) {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [paymentId, setPaymentId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    const createPayment = async () => {
      try {
        setLoading(true);
        setError(null);

        // Create a payment record in our database
        const payment = await stripeService.createPaymentWithDirectTransfer(
          taskId,
          offerId,
          amount
        );

        if (!payment) {
          throw new Error('Failed to create payment record');
        }

        setPaymentId(payment.id);

        // Create a payment intent
        const secret = await stripeService.createPaymentIntent(payment.id);

        if (!secret) {
          throw new Error('Failed to create payment intent');
        }

        setClientSecret(secret);
      } catch (err) {
        console.error('Error creating payment:', err);
        setError(err instanceof Error ? err.message : 'Failed to create payment');
      } finally {
        setLoading(false);
      }
    };

    createPayment();
  }, [taskId, offerId, amount]);

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-center text-muted-foreground">Preparing payment...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className="mb-6">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!clientSecret) {
    return (
      <Alert variant="destructive" className="mb-6">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>Failed to initialize payment. Please try again.</AlertDescription>
      </Alert>
    );
  }

  const options = {
    clientSecret,
    appearance,
  };

  return (
    <div className="w-full">
      <Elements stripe={stripePromise} options={options}>
        <CheckoutForm
          taskId={taskId}
          amount={amount}
          onSuccess={onSuccess}
          onCancel={handleCancel}
        />
      </Elements>
    </div>
  );
}

// Checkout form component
function CheckoutForm({
  taskId,
  amount,
  onSuccess,
  onCancel
}: {
  taskId: string;
  amount: number;
  onSuccess?: () => void;
  onCancel?: () => void;
}) {
  const { user } = useAuth();
  const stripe = useStripe();
  const elements = useElements();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<string>('card');

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Confirm the payment
      const { error: submitError, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: window.location.origin + '/payment-confirmation',
          payment_method_data: {
            billing_details: {
              name: user?.user_metadata?.full_name || 'Unknown',
              email: user?.email || undefined,
            },
          },
          // Save payment method for future use
          redirect: 'if_required'
        }
      });

      if (submitError) {
        throw new Error(submitError.message);
      }

      if (paymentIntent?.status === 'succeeded' || paymentIntent?.status === 'processing') {
        setSuccess(true);

        // Update the task status
        await supabase
          .from('tasks')
          .update({ payment_status: 'processing' })
          .eq('id', taskId);

        // Call the onSuccess callback
        if (onSuccess) {
          onSuccess();
        }
      } else {
        throw new Error('Payment failed');
      }
    } catch (err) {
      console.error('Error processing payment:', err);
      setError(err instanceof Error ? err.message : 'Payment failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Payment Successful</CardTitle>
          <CardDescription>
            Your payment has been processed successfully
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center p-6">
          <CheckCircle2 className="h-16 w-16 text-green-500 mb-4" />
          <p className="text-center text-lg font-medium mb-2">
            Thank you for your payment of £{amount.toFixed(2)}
          </p>
          <p className="text-center text-muted-foreground mb-6">
            The supplier will be notified and the funds will be transferred once the task is completed.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="payment-form">
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Complete Payment</CardTitle>
          <CardDescription>
            Pay £{amount.toFixed(2)} to complete this task
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <LinkAuthenticationElement />

            <div className="payment-element-container">
              <PaymentElement />
            </div>

            <AddressElement options={{ mode: 'billing' }} />

            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={onCancel} disabled={loading}>
            Cancel
          </Button>
          <Button type="submit" disabled={!stripe || loading}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>Pay £{amount.toFixed(2)}</>
            )}
          </Button>
        </CardFooter>
      </Card>
    </form>
  );
}