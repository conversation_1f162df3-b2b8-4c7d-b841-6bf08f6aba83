<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Invitation Tokens</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1, h2 {
            color: #2563eb;
        }
        .card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        button {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #1d4ed8;
        }
        pre {
            background-color: #f3f4f6;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .invitation {
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
        }
        .invitation-header {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .token {
            font-family: monospace;
            background-color: #f3f4f6;
            padding: 2px 4px;
            border-radius: 2px;
        }
        .copy-btn {
            background-color: #4b5563;
            color: white;
            border: none;
            padding: 3px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 5px;
        }
        .copy-btn:hover {
            background-color: #374151;
        }
        .test-link {
            display: block;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>Get Invitation Tokens</h1>
    
    <div class="card">
        <h2>Fetch Invitation Tokens</h2>
        <p>Click the button below to fetch invitation tokens from the database.</p>
        
        <button id="fetchTokensBtn">Fetch Invitation Tokens</button>
        <button id="createTokenBtn">Create Test Invitation</button>
        
        <div id="loadingIndicator" style="display: none; margin-top: 10px;">
            Loading...
        </div>
        
        <div id="invitationsContainer" style="margin-top: 20px;"></div>
    </div>
    
    <div class="card">
        <h2>Test Routes</h2>
        <p>Click the buttons below to test different routes in the application.</p>
        
        <div>
            <button onclick="window.location.href = '/route-test.html'">Go to Route Test Page</button>
            <button onclick="window.location.href = '/invitation-test.html'">Go to Invitation Test Page</button>
        </div>
    </div>
    
    <script>
        document.getElementById('fetchTokensBtn').addEventListener('click', fetchInvitations);
        document.getElementById('createTokenBtn').addEventListener('click', createTestInvitation);
        
        async function fetchInvitations() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const invitationsContainer = document.getElementById('invitationsContainer');
            
            loadingIndicator.style.display = 'block';
            invitationsContainer.innerHTML = '';
            
            try {
                const response = await fetch('/api/invitations');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const invitations = await response.json();
                
                if (invitations.length === 0) {
                    invitationsContainer.innerHTML = '<p>No invitations found.</p>';
                    return;
                }
                
                const invitationsHtml = invitations.map((invitation, index) => {
                    const simpleLink = `/simple-confirmation?token=${invitation.token}`;
                    const directLink = `/direct-confirmation?token=${invitation.token}`;
                    const invitationLink = `/invitation-confirmation?token=${invitation.token}`;
                    
                    return `
                        <div class="invitation">
                            <div class="invitation-header">Invitation ${index + 1}</div>
                            <div><strong>Email:</strong> ${invitation.email}</div>
                            <div><strong>Status:</strong> ${invitation.status}</div>
                            <div><strong>Created:</strong> ${new Date(invitation.created_at).toLocaleString()}</div>
                            <div>
                                <strong>Token:</strong> 
                                <span class="token">${invitation.token}</span>
                                <button class="copy-btn" onclick="copyToClipboard('${invitation.token}')">Copy</button>
                            </div>
                            <div class="test-link">
                                <a href="${simpleLink}" target="_blank">Test with Simple Confirmation</a>
                            </div>
                            <div class="test-link">
                                <a href="${directLink}" target="_blank">Test with Direct Confirmation</a>
                            </div>
                            <div class="test-link">
                                <a href="${invitationLink}" target="_blank">Test with Invitation Confirmation</a>
                            </div>
                        </div>
                    `;
                }).join('');
                
                invitationsContainer.innerHTML = invitationsHtml;
            } catch (error) {
                console.error('Error fetching invitations:', error);
                invitationsContainer.innerHTML = `<p>Error fetching invitations: ${error.message}</p>`;
                
                // Fallback: Use localStorage to simulate invitations
                const token = 'test-token-' + Math.random().toString(36).substring(2, 10);
                const email = '<EMAIL>';
                
                invitationsContainer.innerHTML += `
                    <div class="invitation">
                        <div class="invitation-header">Test Invitation (Simulated)</div>
                        <div><strong>Email:</strong> ${email}</div>
                        <div><strong>Status:</strong> pending</div>
                        <div><strong>Created:</strong> ${new Date().toLocaleString()}</div>
                        <div>
                            <strong>Token:</strong> 
                            <span class="token">${token}</span>
                            <button class="copy-btn" onclick="copyToClipboard('${token}')">Copy</button>
                        </div>
                        <div class="test-link">
                            <a href="/simple-confirmation?token=${token}" target="_blank">Test with Simple Confirmation</a>
                        </div>
                        <div class="test-link">
                            <a href="/direct-confirmation?token=${token}" target="_blank">Test with Direct Confirmation</a>
                        </div>
                        <div class="test-link">
                            <a href="/invitation-confirmation?token=${token}" target="_blank">Test with Invitation Confirmation</a>
                        </div>
                    </div>
                `;
            } finally {
                loadingIndicator.style.display = 'none';
            }
        }
        
        async function createTestInvitation() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const invitationsContainer = document.getElementById('invitationsContainer');
            
            loadingIndicator.style.display = 'block';
            
            try {
                const response = await fetch('/api/create-invitation', {
                    method: 'POST'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const invitation = await response.json();
                
                // After creating, fetch all invitations
                fetchInvitations();
            } catch (error) {
                console.error('Error creating invitation:', error);
                invitationsContainer.innerHTML = `<p>Error creating invitation: ${error.message}</p>`;
                loadingIndicator.style.display = 'none';
                
                // Fallback: Create a simulated invitation
                const token = 'test-token-' + Math.random().toString(36).substring(2, 10);
                const email = '<EMAIL>';
                
                invitationsContainer.innerHTML += `
                    <div class="invitation">
                        <div class="invitation-header">Test Invitation (Simulated)</div>
                        <div><strong>Email:</strong> ${email}</div>
                        <div><strong>Status:</strong> pending</div>
                        <div><strong>Created:</strong> ${new Date().toLocaleString()}</div>
                        <div>
                            <strong>Token:</strong> 
                            <span class="token">${token}</span>
                            <button class="copy-btn" onclick="copyToClipboard('${token}')">Copy</button>
                        </div>
                        <div class="test-link">
                            <a href="/simple-confirmation?token=${token}" target="_blank">Test with Simple Confirmation</a>
                        </div>
                        <div class="test-link">
                            <a href="/direct-confirmation?token=${token}" target="_blank">Test with Direct Confirmation</a>
                        </div>
                        <div class="test-link">
                            <a href="/invitation-confirmation?token=${token}" target="_blank">Test with Invitation Confirmation</a>
                        </div>
                    </div>
                `;
            }
        }
        
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                alert('Token copied to clipboard!');
            }).catch(err => {
                console.error('Could not copy text: ', err);
            });
        }
        
        // Fetch invitations on page load
        fetchInvitations();
    </script>
</body>
</html>
