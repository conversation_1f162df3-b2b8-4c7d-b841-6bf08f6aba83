#!/bin/bash

# Start script for running the application with Stripe Connect integration

# Load environment variables
if [ -f .env ]; then
  export $(cat .env | grep -v '#' | awk '/=/ {print $1}')
fi

# Check if required environment variables are set in .env (server-side)
if [ -z "$STRIPE_SECRET_KEY" ] || [ -z "$STRIPE_WEBHOOK_SECRET" ]; then
  echo "Error: Missing required server-side Stripe environment variables."
  echo "Please make sure STRIPE_SECRET_KEY and STRIPE_WEBHOOK_SECRET are set in your .env file."
  exit 1
fi

# Check if .env.local exists and has required variables
if [ -f .env.local ]; then
  source .env.local
  if [ -z "$VITE_STRIPE_PUBLIC_KEY" ]; then
    echo "Warning: Missing VITE_STRIPE_PUBLIC_KEY in .env.local file."
    echo "The frontend may not work correctly without this variable."
  fi
else
  echo "Warning: .env.local file not found."
  echo "The frontend may not work correctly without the required environment variables."
fi

# Apply Stripe Connect database setup
echo "Applying Stripe Connect database setup..."
npm run setup-stripe-connect

# Check if the setup was successful
if [ $? -ne 0 ]; then
  echo "Error: Failed to apply Stripe Connect database setup."
  exit 1
fi

# Start the application with the server
echo "Starting the application with Stripe Connect integration..."
npm run dev:all

# Exit with the status of the last command
exit $?