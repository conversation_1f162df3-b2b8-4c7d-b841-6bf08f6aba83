// Simple test function for Resend API
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

// Define CORS headers directly
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type"
};

// Main handler function
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    console.log("Testing Resend API...");
    
    // Get the Resend API key from environment variables
    const RESEND_API_KEY = Deno.env.get("RESEND_API_KEY");
    
    if (!RESEND_API_KEY) {
      return new Response(
        JSON.stringify({ error: "RESEND_API_KEY environment variable is not set" }),
        { 
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }
    
    console.log("RESEND_API_KEY is set");
    
    try {
      // Use Resend API directly
      const response = await fetch("https://api.resend.com/emails", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${RESEND_API_KEY}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          from: "<EMAIL>",
          to: "<EMAIL>",
          subject: "Test Email from Edge Function",
          html: `
            <h2>Test Email from Edge Function</h2>
            <p>This is a test email sent directly from the Edge Function to verify that email sending is working.</p>
            <p>If you're seeing this, the Resend API is working correctly!</p>
            <p>Timestamp: ${new Date().toISOString()}</p>
          `
        })
      });
      
      const responseData = await response.json();
      
      console.log("Resend API response:", responseData);
      
      if (!response.ok) {
        return new Response(
          JSON.stringify({ 
            error: "Failed to send email via Resend API", 
            details: responseData 
          }),
          { 
            status: 500,
            headers: { ...corsHeaders, "Content-Type": "application/json" }
          }
        );
      }
      
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: "Test email sent successfully", 
          data: responseData 
        }),
        { 
          status: 200,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      console.error("Error sending email:", error);
      
      return new Response(
        JSON.stringify({ 
          error: "Failed to send email", 
          details: error.message || "Unknown error"
        }),
        { 
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }
  } catch (error) {
    console.error("Error processing request:", error);
    
    return new Response(
      JSON.stringify({ error: "Failed to process request" }),
      { 
        status: 500,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        }
      }
    );
  }
});
