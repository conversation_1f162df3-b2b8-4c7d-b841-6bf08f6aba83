import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { MapPin, Calendar, PoundSterling, ChevronRight, AlertCircle } from "lucide-react";
import { format } from "date-fns";
import { Task } from "@/services/taskService";

interface PWATaskCardProps {
  task: Task;
}

const PWATaskCard: React.FC<PWATaskCardProps> = ({ task }) => {
  const navigate = useNavigate();

  // Function to get status badge variant
  const getStatusVariant = (status: string, visibility?: string) => {
    // Special case for admin review tasks
    if (visibility === 'admin' && status === 'open') {
      return 'bg-purple-100 text-purple-800 border-purple-200';
    }

    switch (status) {
      case 'open':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'interest':
      case 'questions':
        return 'bg-cyan-100 text-cyan-800 border-cyan-200';
      case 'offer':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'assigned':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'in_progress':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'completed':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'confirmed':
        return 'bg-teal-100 text-teal-800 border-teal-200';
      case 'pending_payment':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'admin_review':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Format status for display
  const formatStatus = (status: string, visibility?: string) => {
    // Special case for admin review tasks
    if (visibility === 'admin' && status === 'open') {
      return 'Admin Review';
    }

    switch (status) {
      case 'pending_payment':
        return 'Payment Required';
      case 'in_progress':
        return 'In Progress';
      case 'interest':
        return 'Interest Expressed';
      case 'questions':
        return 'Discussion Phase';
      case 'admin_review':
        return 'Admin Review';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  // Extract location display (just the town/city)
  const extractTownFromLocation = (loc: string): string => {
    if (!loc) return '';
    const parts = loc.split(',').map(part => part.trim());
    if (parts.length >= 2) {
      return parts[1]; // Usually the town/city is the second part
    }
    return loc;
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return 'No due date';
    try {
      return format(new Date(dateString), 'dd MMM yyyy');
    } catch (e) {
      return 'Invalid date';
    }
  };

  const handleClick = () => {
    navigate(`/tasks/${task.id}`);
  };

  return (
    <div
      className="bg-white rounded-lg p-4 border border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors"
      onClick={handleClick}
    >
      <div className="flex items-start space-x-3">
        {/* Content */}
        <div className="flex-1 min-w-0">
          {/* Title */}
          <h3 className="font-medium text-gray-900 mb-1 flex items-center">
            {task.title}
            {task.visibility === 'admin' && task.status === 'open' && !task.assigned_to && (
              <AlertCircle className="h-4 w-4 text-purple-600 ml-2 flex-shrink-0" />
            )}
          </h3>

          {/* Description if available */}
          {task.description && (
            <p className="text-sm text-gray-600 mb-2 line-clamp-2">
              {task.description}
            </p>
          )}

          {/* Badges and Metadata */}
          <div className="flex flex-wrap items-center gap-2 text-sm">
            {/* Status Badge */}
            <Badge className={getStatusVariant(task.status, task.visibility)}>
              {formatStatus(task.status, task.visibility)}
            </Badge>

            {/* Budget Badge */}
            {task.budget > 0 && (
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                <PoundSterling className="h-3 w-3 mr-1" />
                {task.budget.toFixed(2)}
              </Badge>
            )}

            {/* Location */}
            {task.location && (
              <span className="text-gray-500 flex items-center">
                <MapPin className="h-3 w-3 mr-1" />
                {extractTownFromLocation(task.location)}
              </span>
            )}

            {/* Due Date */}
            {task.due_date && (
              <span className="text-gray-500 flex items-center">
                <Calendar className="h-3 w-3 mr-1" />
                Due: {formatDate(task.due_date)}
              </span>
            )}
          </div>
        </div>

        {/* Chevron */}
        <div className="pt-1">
          <ChevronRight className="h-5 w-5 text-gray-400" />
        </div>
      </div>
    </div>
  );
};

export default PWATaskCard;
