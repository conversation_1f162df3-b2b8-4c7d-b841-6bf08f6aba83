import React, { useState, useEffect } from 'react';
import { useP<PERSON><PERSON>, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  ArrowLeft,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Thum<PERSON>Up,
  Loader2,
  User,
  DollarSign,
  PlayCircle,
  HandCoins,
  Send,
  Globe,
  Building
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { isInRoleGroup } from '@/constants/roles';
import { ROLE_GROUPS } from '@/constants/roles';

/**
 * Final version of PWATaskActions component
 * This component shows task actions based on user role and task status
 */
const PWATaskActionsFinal: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user, profile, userRole, isAdmin, isSupplier } = useAuth();
  const [task, setTask] = useState<any>(null);
  const [creatorProfile, setCreatorProfile] = useState<any>(null);
  const [assigneeProfile, setAssigneeProfile] = useState<any>(null);
  const [userOffer, setUserOffer] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State for assignment dialog
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [assignmentType, setAssignmentType] = useState<'internal' | 'public'>('internal');
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  const [taskBudget, setTaskBudget] = useState<string>('');
  const [organizationUsers, setOrganizationUsers] = useState<any[]>([]);
  const [staffRoleFilter, setStaffRoleFilter] = useState<string>('all');

  // State for offer dialog
  const [offerDialogOpen, setOfferDialogOpen] = useState(false);
  const [offerAmount, setOfferAmount] = useState<string>('');
  const [offerMessage, setOfferMessage] = useState<string>('');

  // Fetch task data
  const fetchTask = async () => {
    setLoading(true);
    setError(null);

    try {
      if (!id || !user) {
        setError('Task ID is missing or user is not logged in');
        return;
      }

      // Fetch task from API
      const { data: taskData, error: taskError } = await supabase
        .from('tasks')
        .select('*')
        .eq('id', id)
        .single();

      if (taskError) {
        console.error('[PWATaskActionsFinal] Error fetching task:', taskError);
        throw taskError;
      }

      if (!taskData) {
        setError('Task not found');
        return;
      }

      console.log('[PWATaskActionsFinal] Task data retrieved:', taskData);
      setTask(taskData);

      // Initialize offer amount with task budget if available
      if (taskData.budget) {
        setOfferAmount(taskData.budget.toString());
      }

      // Fetch creator profile
      if (taskData.user_id) {
        const { data: creatorData, error: creatorError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', taskData.user_id)
          .single();

        if (!creatorError && creatorData) {
          setCreatorProfile(creatorData);
        }
      }

      // Fetch assignee profile
      if (taskData.assigned_to) {
        const { data: assigneeData, error: assigneeError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', taskData.assigned_to)
          .single();

        if (!assigneeError && assigneeData) {
          setAssigneeProfile(assigneeData);
        }
      }

      // If user is a supplier, check if they have an offer for this task
      if (isSupplier && taskData.visibility === 'public') {
        const { data: offerData, error: offerError } = await supabase
          .from('offers')
          .select('*')
          .eq('task_id', id)
          .eq('user_id', user.id)
          .maybeSingle();

        if (!offerError && offerData) {
          setUserOffer(offerData);
        }
      }
    } catch (error: any) {
      console.error('[PWATaskActionsFinal] Error fetching task:', error);
      setError(error.message || 'Failed to load task');
    } finally {
      setLoading(false);
    }
  };

  // Fetch organization users for assignment
  const fetchOrganizationUsers = async () => {
    try {
      if (!user || !profile?.organization_id) {
        console.error('[PWATaskActionsFinal] Cannot fetch users: No user or organization ID');
        return;
      }

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('organization_id', profile.organization_id)
        .order('first_name', { ascending: true });

      if (error) {
        console.error('[PWATaskActionsFinal] Error fetching organization users:', error);
        return;
      }

      console.log('[PWATaskActionsFinal] Organization users:', data);
      setOrganizationUsers(data || []);
    } catch (error) {
      console.error('[PWATaskActionsFinal] Error fetching organization users:', error);
    }
  };

  // Initial data fetch
  useEffect(() => {
    console.log('[PWATaskActionsFinal] Component mounted with task ID:', id);
    fetchTask();

    // If user is admin, fetch organization users for assignment
    if (isAdmin && user && profile?.organization_id) {
      fetchOrganizationUsers();
    }
  }, [id, isAdmin, user, profile?.organization_id]);

  // Handle back button
  const handleBack = () => {
    navigate(-1);
  };

  // Handle opening the assignment dialog
  const handleOpenAssignDialog = () => {
    if (!task) return;

    // Initialize budget with current task budget
    setTaskBudget(task.budget ? task.budget.toString() : '0');
    setAssignmentType('internal');
    setSelectedUserId('');
    setStaffRoleFilter('all');
    setAssignDialogOpen(true);
  };

  // Handle opening the offer dialog
  const handleOpenOfferDialog = () => {
    if (!task) return;

    // Initialize with existing offer data if available
    if (userOffer) {
      setOfferAmount(userOffer.amount.toString());
      setOfferMessage(userOffer.message || '');
    } else if (task.budget) {
      // Otherwise initialize with task budget
      setOfferAmount(task.budget.toString());
      // Default message for new offers
      setOfferMessage(`I can complete this task for £${parseFloat(task.budget).toFixed(2)}.`);
    }

    setOfferDialogOpen(true);
  };

  // Handle submitting the offer
  const handleSubmitOffer = async () => {
    if (!task || !user) return;

    if (!offerAmount || !offerMessage) {
      toast({
        variant: "destructive",
        title: "Missing information",
        description: "Please provide both an amount and a message for your offer.",
      });
      return;
    }

    // Close dialog and submit the offer
    setOfferDialogOpen(false);
    await handleTaskAction('submit_offer', {
      amount: parseFloat(offerAmount),
      message: offerMessage
    });
  };

  // Handle task assignment
  const handleAssignTask = async () => {
    if (!task || !user) return;

    setActionLoading(true);

    try {
      // Validate inputs
      if (assignmentType === 'internal' && !selectedUserId) {
        throw new Error('Please select a staff member to assign the task to');
      }

      if (assignmentType === 'public') {
        const budget = parseFloat(taskBudget);
        if (isNaN(budget) || budget <= 0) {
          throw new Error('Please enter a valid budget for the public task');
        }
      }

      // Prepare update data based on assignment type
      const updateData: any = assignmentType === 'public'
        ? {
            visibility: 'public',
            status: 'open', // For public visibility, we keep it as 'open' until a supplier offer is accepted
            assigned_to: null, // Clear any existing assignment
            budget: parseFloat(taskBudget),
            updated_at: new Date().toISOString()
          }
        : {
            assigned_to: selectedUserId,
            visibility: 'internal',
            status: 'assigned', // For internal assignments, set status to assigned
            payment_status: 'not_required', // For internal assignments, set payment_status to not_required
            updated_at: new Date().toISOString()
          };

      // Update the task
      const { error } = await supabase
        .from('tasks')
        .update(updateData)
        .eq('id', task.id);

      if (error) {
        throw error;
      }

      // Show success message
      toast({
        title: 'Success',
        description: assignmentType === 'public'
          ? 'Task has been made public and is now visible to suppliers'
          : 'Task has been assigned to the selected staff member',
      });

      // Close dialog and refresh task data
      setAssignDialogOpen(false);
      fetchTask();
    } catch (error: any) {
      console.error('[PWATaskActionsFinal] Error assigning task:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Failed to assign task',
      });
    } finally {
      setActionLoading(false);
    }
  };

  // Handle task action
  const handleTaskAction = async (action: string, additionalData?: any) => {
    if (!task || !user) return;

    setActionLoading(true);

    try {
      let newStatus = '';
      let message = '';
      let updateData: any = {};

      switch (action) {
        // Action for assigning tasks pending review
        case 'assign_task':
          // Open the assignment dialog
          handleOpenAssignDialog();
          setActionLoading(false);
          return; // Exit early since we're just opening a dialog

        // Actions for assigned staff
        case 'start':
          newStatus = 'in_progress';
          message = 'Task started successfully';
          updateData = {
            status: newStatus,
            updated_at: new Date().toISOString()
          };
          break;

        case 'complete':
          newStatus = 'completed';
          message = 'Task marked as completed';
          updateData = {
            status: newStatus,
            updated_at: new Date().toISOString(),
            completed_at: new Date().toISOString()
          };
          break;

        // Actions for task creators/admins
        case 'assign':
          if (!additionalData?.assignedTo) {
            throw new Error('No assignee specified');
          }
          newStatus = 'assigned';
          message = 'Task assigned successfully';
          updateData = {
            status: newStatus,
            assigned_to: additionalData.assignedTo,
            updated_at: new Date().toISOString()
          };
          break;

        case 'confirm':
          newStatus = 'confirmed';
          message = 'Task completion confirmed';
          updateData = {
            status: newStatus,
            updated_at: new Date().toISOString()
          };
          break;

        case 'request_payment':
          newStatus = 'pending_payment';
          message = 'Payment requested';
          updateData = {
            status: newStatus,
            updated_at: new Date().toISOString()
          };
          break;

        // Actions for suppliers
        case 'express_interest':
          // For suppliers expressing interest, we don't change task status
          // Instead, we create a GetStream chat channel
          message = 'Interest expressed successfully';

          try {
            // Import the necessary functions
            const { getStreamClient, createOrUpdateTaskChannel } = await import('@/integrations/getstream/client');

            // Create or update the GetStream channel
            console.log('[PWATaskActionsFinal] Creating GetStream channel for task:', task.id);

            // Create the channel with the task creator and the supplier
            const channel = await createOrUpdateTaskChannel(
              task.id,
              task.title,
              [user.id, task.user_id],
              true
            );

            // Send a system message
            await channel.sendMessage({
              text: `${user.email || 'A supplier'} has expressed interest in this task.`,
              user_id: user.id,
              type: 'system',
            });

            console.log('[PWATaskActionsFinal] Successfully created GetStream channel for task:', task.id);
          } catch (error) {
            console.error('[PWATaskActionsFinal] Error creating GetStream channel:', error);
            throw new Error('Failed to create chat channel. Please try again.');
          }

          // Update task status to 'interest' if it's currently 'open'
          if (task.status === 'open') {
            updateData = {
              status: 'interest',
              updated_at: new Date().toISOString()
            };
          }
          break;

        case 'submit_offer':
          // If no additional data is provided, show the offer dialog
          if (!additionalData) {
            handleOpenOfferDialog();
            setActionLoading(false);
            return; // Exit early since we're just opening a dialog
          }

          if (!additionalData.amount) {
            throw new Error('No offer amount specified');
          }

          message = 'Offer submitted successfully';

          // Create or update an offer
          if (userOffer) {
            // Update existing offer
            const { error: updateOfferError } = await supabase
              .from('offers')
              .update({
                amount: additionalData.amount,
                message: additionalData.message || '',
                updated_at: new Date().toISOString()
              })
              .eq('id', userOffer.id);

            if (updateOfferError) {
              throw updateOfferError;
            }
          } else {
            // Create new offer
            const { error: createOfferError } = await supabase
              .from('offers')
              .insert({
                task_id: task.id,
                user_id: user.id,
                amount: additionalData.amount,
                message: additionalData.message || '',
                status: 'pending'
              });

            if (createOfferError) {
              throw createOfferError;
            }
          }

          // Update task status to 'offer' if it's currently 'interest' or 'questions'
          if (task.status === 'interest' || task.status === 'questions' || task.status === 'open') {
            updateData = {
              status: 'offer',
              updated_at: new Date().toISOString()
            };
          }

          // Always update the offers_count field when a new offer is created
          if (!userOffer) {
            // Get the current count
            const { data: countData, error: countError } = await supabase
              .from('offers')
              .select('count')
              .eq('task_id', task.id);

            if (!countError && countData) {
              const offerCount = countData.length;

              // Update the offers_count field
              const { error: updateCountError } = await supabase
                .from('tasks')
                .update({ offers_count: offerCount })
                .eq('id', task.id);

              if (updateCountError) {
                console.error('[PWATaskActionsFinal] Error updating offers count:', updateCountError);
              }
            }
          }
          break;

        // Common actions
        case 'cancel':
          newStatus = 'cancelled';
          message = 'Task cancelled';
          updateData = {
            status: newStatus,
            updated_at: new Date().toISOString()
          };
          break;

        case 'accept':
          newStatus = 'assigned';
          message = 'Offer accepted';

          // For accepting an offer, we need to update the task status and assigned_to
          if (!additionalData?.offerId || !additionalData?.supplierId) {
            throw new Error('Missing offer information');
          }

          updateData = {
            status: newStatus,
            assigned_to: additionalData.supplierId,
            updated_at: new Date().toISOString()
          };

          // Also update the offer status
          const { error: offerError } = await supabase
            .from('offers')
            .update({ status: 'accepted' })
            .eq('id', additionalData.offerId);

          if (offerError) {
            throw offerError;
          }
          break;

        default:
          throw new Error('Invalid action');
      }

      // Update task if there are changes to make
      if (Object.keys(updateData).length > 0) {
        const { error } = await supabase
          .from('tasks')
          .update(updateData)
          .eq('id', task.id);

        if (error) {
          throw error;
        }
      }

      // Show success message
      toast({
        title: 'Success',
        description: message,
      });

      // Refresh task data
      fetchTask();
    } catch (error: any) {
      console.error('[PWATaskActionsFinal] Error updating task:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Failed to update task',
      });
    } finally {
      setActionLoading(false);
    }
  };

  // Get available actions based on task status and user role
  const getAvailableActions = () => {
    if (!task || !user || !profile) return [];

    const isCreator = task.user_id === user.id;
    const isAssigned = task.assigned_to === user.id;
    const status = task.status?.toLowerCase() || '';
    const visibility = task.visibility;
    const isInternalTask = visibility === 'internal';
    const isExternalTask = visibility === 'public';
    const isAssignableStaff = isInRoleGroup(userRole, ROLE_GROUPS.TASK_ASSIGNABLE);

    const actions = [];

    // ADMIN ACTIONS
    if (isAdmin) {
      // For tasks pending review (status is 'open' and visibility is 'admin'), admins can assign them
      if (status === 'open' && task.visibility === 'admin') {
        actions.push({
          label: 'Assign Task',
          action: 'assign_task',
          icon: <User className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }

      // For open tasks with internal visibility, admins can assign them
      if (status === 'open' && isInternalTask) {
        actions.push({
          label: 'Assign Task',
          action: 'assign',
          icon: <User className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }

      // For completed tasks, admins can confirm completion
      if (status === 'completed') {
        actions.push({
          label: 'Confirm Completion',
          action: 'confirm',
          icon: <CheckCircle className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }

      // For confirmed tasks, admins can request payment (external tasks)
      if (status === 'confirmed' && isExternalTask) {
        actions.push({
          label: 'Request Payment',
          action: 'request_payment',
          icon: <DollarSign className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }

      // For tasks with offers, admins can accept an offer
      if (status === 'offer' && isExternalTask) {
        actions.push({
          label: 'Accept Offer',
          action: 'accept',
          icon: <ThumbsUp className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }

      // Admins can cancel tasks that aren't completed or confirmed
      if (!['completed', 'confirmed', 'pending_payment', 'cancelled'].includes(status)) {
        actions.push({
          label: 'Cancel Task',
          action: 'cancel',
          icon: <XCircle className="h-4 w-4 mr-2" />,
          variant: 'destructive' as const
        });
      }
    }

    // TASK CREATOR ACTIONS (if not admin)
    else if (isCreator) {
      // For completed tasks, creators can confirm completion
      if (status === 'completed') {
        actions.push({
          label: 'Confirm Completion',
          action: 'confirm',
          icon: <CheckCircle className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }

      // For tasks with offers, creators can accept an offer
      if (status === 'offer' && isExternalTask) {
        actions.push({
          label: 'Accept Offer',
          action: 'accept',
          icon: <ThumbsUp className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }

      // Creators can cancel tasks that aren't completed or confirmed
      if (!['completed', 'confirmed', 'pending_payment', 'cancelled'].includes(status)) {
        actions.push({
          label: 'Cancel Task',
          action: 'cancel',
          icon: <XCircle className="h-4 w-4 mr-2" />,
          variant: 'destructive' as const
        });
      }
    }

    // ASSIGNED STAFF ACTIONS
    if (isAssigned) {
      // Check if user is admin or other assignable staff
      const canPerformTaskActions = isAdmin || isAssignableStaff;

      if (canPerformTaskActions) {
        // For assigned tasks, staff can start them
        if (status === 'assigned') {
          actions.push({
            label: 'Start Task',
            action: 'start',
            icon: <PlayCircle className="h-4 w-4 mr-2" />,
            variant: 'default' as const
          });
        }

        // For in-progress tasks, staff can mark them as completed
        if (status === 'in_progress') {
          actions.push({
            label: 'Mark as Completed',
            action: 'complete',
            icon: <CheckCircle className="h-4 w-4 mr-2" />,
            variant: 'default' as const
          });
        }
      }
    }

    // SUPPLIER ACTIONS
    if (isSupplier && isExternalTask) {
      // For open tasks, suppliers can express interest
      if (status === 'open' || status === 'interest') {
        // Only show if the supplier hasn't already expressed interest
        if (!userOffer) {
          actions.push({
            label: 'Express Interest',
            action: 'express_interest',
            icon: <HandCoins className="h-4 w-4 mr-2" />,
            variant: 'default' as const
          });
        }
      }

      // For tasks in interest or questions phase, suppliers can submit offers
      if (['open', 'interest', 'questions'].includes(status)) {
        actions.push({
          label: userOffer ? 'Update Offer' : 'Submit Offer',
          action: 'submit_offer',
          icon: <Send className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }

      // For assigned tasks (to this supplier), they can start work
      if (status === 'assigned' && isAssigned) {
        actions.push({
          label: 'Start Work',
          action: 'start',
          icon: <PlayCircle className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }

      // For in-progress tasks (assigned to this supplier), they can mark as completed
      if (status === 'in_progress' && isAssigned) {
        actions.push({
          label: 'Mark as Completed',
          action: 'complete',
          icon: <CheckCircle className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }
    }

    return actions;
  };

  console.log('[PWATaskActionsFinal] Rendering component with task:', task?.id, 'and status:', task?.status);

  return (
    <div className="container max-w-md mx-auto px-4 py-6">
      {/* Header with back button */}
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="icon" onClick={handleBack} className="mr-2">
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-xl font-semibold">Task Actions</h1>
      </div>

      {/* Confirmation message that this is the new version */}
      <div className="bg-green-100 border border-green-300 rounded-md p-3 mb-4 text-green-800 text-sm">
        <p className="font-medium">✅ Enhanced Task Actions (Final Version)</p>
        <p>This is the updated version with role-specific actions.</p>
        <p className="text-xs mt-1 text-green-600">Updated: {new Date().toLocaleString()}</p>
      </div>

      {/* Error state */}
      {error && (
        <Card className="mb-6">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-2" />
            <p className="text-red-500">{error}</p>
            <Button variant="outline" className="mt-3" onClick={fetchTask}>
              Try Again
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Loading state */}
      {loading && (
        <div className="space-y-4">
          <Skeleton className="h-8 w-3/4" />
          <Skeleton className="h-6 w-1/3" />
          <Skeleton className="h-32 w-full" />
        </div>
      )}

      {/* Task content */}
      {!loading && task && (
        <>
          {/* Task info */}
          <Card className="mb-6">
            <CardContent className="p-4">
              <h2 className="text-lg font-semibold mb-2">{task.title}</h2>
              <div className="flex items-center mb-4">
                <Badge className="text-sm capitalize">
                  {task.status || 'Open'}
                </Badge>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                {task.description || 'No description provided'}
              </p>
            </CardContent>
          </Card>

          {/* Available actions */}
          <div className="space-y-3">
            <h3 className="text-lg font-medium mb-2">Available Actions</h3>

            {getAvailableActions().length === 0 ? (
              <Card>
                <CardContent className="p-4 text-center">
                  <p className="text-gray-500">No actions available for this task</p>
                </CardContent>
              </Card>
            ) : (
              getAvailableActions().map((action, index) => (
                <Button
                  key={index}
                  variant={action.variant}
                  className="w-full justify-start text-left h-auto py-3"
                  onClick={() => handleTaskAction(action.action)}
                  disabled={actionLoading}
                >
                  {actionLoading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    action.icon
                  )}
                  {action.label}
                </Button>
              ))
            )}
          </div>
        </>
      )}

      {/* Assignment Dialog */}
      <Dialog open={assignDialogOpen} onOpenChange={setAssignDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Assign Task</DialogTitle>
            <DialogDescription>
              Assign "{task?.title}" to a staff member or make it available to suppliers.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Assignment Type Selection */}
            <div className="space-y-2">
              <Label>Assignment Type</Label>
              <RadioGroup
                value={assignmentType}
                onValueChange={(value) => setAssignmentType(value as 'internal' | 'public')}
                className="flex flex-col space-y-1"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="internal" id="internal" />
                  <Label htmlFor="internal" className="flex items-center">
                    <Building className="h-4 w-4 mr-2 text-blue-500" />
                    Assign to Staff Member
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="public" id="public" />
                  <Label htmlFor="public" className="flex items-center">
                    <Globe className="h-4 w-4 mr-2 text-green-500" />
                    Make Public for Suppliers
                  </Label>
                </div>
              </RadioGroup>
            </div>

            {/* Internal Assignment Options */}
            {assignmentType === 'internal' && (
              <div className="space-y-2">
                <Label>Select Staff Member</Label>
                <div className="space-y-2">
                  <Select
                    value={staffRoleFilter}
                    onValueChange={setStaffRoleFilter}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Filter by role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Roles</SelectItem>
                      <SelectItem value="maintenance">Maintenance</SelectItem>
                      <SelectItem value="admin">Admin</SelectItem>
                      <SelectItem value="support">Support</SelectItem>
                      <SelectItem value="it">IT</SelectItem>
                      <SelectItem value="cleaning">Cleaning</SelectItem>
                    </SelectContent>
                  </Select>

                  <div className="max-h-60 overflow-y-auto border rounded-md p-2">
                    {organizationUsers
                      .filter(user => {
                        // Exclude teachers from assignable users
                        if (user.role === 'teacher') return false;

                        // Filter by selected role if not "all"
                        return staffRoleFilter === 'all' || user.role === staffRoleFilter;
                      })
                      .map(user => (
                        <div
                          key={user.id}
                          className={`p-2 rounded-md cursor-pointer mb-1 ${
                            selectedUserId === user.id
                              ? 'bg-blue-100 border border-blue-300'
                              : 'hover:bg-gray-100'
                          }`}
                          onClick={() => setSelectedUserId(user.id)}
                        >
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-2 text-gray-500" />
                            <div>
                              <p className="font-medium">
                                {user.first_name} {user.last_name}
                              </p>
                              <p className="text-xs text-gray-500 capitalize">
                                {user.role || 'No role'}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}

                    {organizationUsers.filter(user => {
                      // Exclude teachers from assignable users
                      if (user.role === 'teacher') return false;

                      // Filter by selected role if not "all"
                      return staffRoleFilter === 'all' || user.role === staffRoleFilter;
                    }).length === 0 && (
                      <p className="text-center text-gray-500 py-4">
                        No assignable staff members found
                        {staffRoleFilter !== 'all' && ` with role: ${staffRoleFilter}`}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Public Assignment Options */}
            {assignmentType === 'public' && (
              <div className="space-y-2">
                <Label htmlFor="budget">Budget (£)</Label>
                <Input
                  id="budget"
                  type="number"
                  min="0"
                  step="0.01"
                  value={taskBudget}
                  onChange={(e) => setTaskBudget(e.target.value)}
                  placeholder="Enter budget amount"
                />
                <p className="text-xs text-gray-500">
                  Set a budget for suppliers to bid on this task
                </p>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setAssignDialogOpen(false)}
              disabled={actionLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAssignTask}
              disabled={
                actionLoading ||
                (assignmentType === 'internal' && !selectedUserId) ||
                (assignmentType === 'public' && (!taskBudget || parseFloat(taskBudget) <= 0))
              }
            >
              {actionLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                assignmentType === 'public' ? 'Make Public' : 'Assign Task'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Offer Dialog */}
      <Dialog open={offerDialogOpen} onOpenChange={setOfferDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{userOffer ? 'Update Your Offer' : 'Submit Your Offer'}</DialogTitle>
            <DialogDescription>
              Provide your price and a message explaining your offer.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="offerAmount" className="text-left">
                Your Price (£)
              </Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <span className="text-gray-500 font-medium">£</span>
                </div>
                <Input
                  id="offerAmount"
                  type="number"
                  step="0.01"
                  min="1"
                  value={offerAmount}
                  onChange={(e) => setOfferAmount(e.target.value)}
                  className="pl-9"
                  required
                />
              </div>
              {task?.budget && (
                <p className="text-xs text-gray-500">
                  Task budget: £{parseFloat(task.budget).toFixed(2)}
                </p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="offerMessage" className="text-left">
                Message
              </Label>
              <Textarea
                id="offerMessage"
                value={offerMessage}
                onChange={(e) => setOfferMessage(e.target.value)}
                placeholder="Explain your price and what you can offer..."
                rows={4}
                required
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setOfferDialogOpen(false)}>
              Cancel
            </Button>
            <Button type="button" onClick={handleSubmitOffer}>
              {userOffer ? 'Update Offer' : 'Submit Offer'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PWATaskActionsFinal;