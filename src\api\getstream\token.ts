/**
 * GetStream Token Generator API
 *
 * This API endpoint generates a token for GetStream Chat authentication.
 * It uses the GetStream server SDK to create a token for the specified user.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { StreamChat } from 'stream-chat';

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Initialize GetStream client
const streamApiKey = import.meta.env.VITE_GETSTREAM_API_KEY;
const streamApiSecret = import.meta.env.GETSTREAM_API_SECRET;

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get the user ID from the request body
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Verify the user is authenticated
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Verify the user is requesting their own token
    if (session.user.id !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // Initialize the GetStream client
    if (!streamApiKey || !streamApiSecret) {
      return res.status(500).json({ error: 'GetStream API credentials not configured' });
    }

    const serverClient = StreamChat.getInstance(streamApiKey, streamApiSecret);

    // Generate a token for the user with full permissions
    const token = serverClient.createToken(userId, {
      // Grant read/write access to all channels
      exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24, // 24 hour expiration
      // Add explicit permissions for messaging channels
      "iat": Math.floor(Date.now() / 1000),
      "resource": "*",
      "action": "*",
      "feed_id": "*",
      "user_id": userId
    });

    // Return the token
    return res.status(200).json({ token });
  } catch (error) {
    console.error('Error generating GetStream token:', error);
    return res.status(500).json({ error: 'Failed to generate token' });
  }
}
