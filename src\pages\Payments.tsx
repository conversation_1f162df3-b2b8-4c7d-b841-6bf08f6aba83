
import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CreditCard, PoundSterling, FilePlus, Clock } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

const Payments = () => {
  const handleAddPaymentMethod = () => {
    toast({
      title: "Feature in development",
      description: "Payment methods functionality is coming soon",
    });
  };

  return (
    <MainLayout>
      <div className="container mx-auto py-12 px-4">
        <h1 className="text-3xl font-bold mb-8">Payments & Billing</h1>

        <div className="grid gap-8 md:grid-cols-3">
          <div className="md:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PoundSterling className="mr-2 h-5 w-5 text-classtasker-blue" />
                  Payment Methods
                </CardTitle>
                <CardDescription>Manage your payment methods</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 border rounded-md flex items-center justify-between">
                    <div className="flex items-center">
                      <CreditCard className="h-6 w-6 mr-4 text-gray-600" />
                      <div>
                        <p className="font-medium">•••• •••• •••• 4242</p>
                        <p className="text-sm text-gray-500">Expires 12/25</p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      Remove
                    </Button>
                  </div>

                  <Button
                    variant="outline"
                    className="w-full flex items-center justify-center"
                    onClick={handleAddPaymentMethod}
                  >
                    <FilePlus className="mr-2 h-5 w-5" />
                    Add Payment Method
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Transaction History</CardTitle>
                <CardDescription>Your recent payments and earnings</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 border-b">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">Classroom Repair</p>
                        <p className="text-sm text-gray-500">May 15, 2025</p>
                      </div>
                      <span className="text-red-500 font-medium">-$120.00</span>
                    </div>
                  </div>

                  <div className="p-4 border-b">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">Window Installation</p>
                        <p className="text-sm text-gray-500">May 10, 2025</p>
                      </div>
                      <span className="text-red-500 font-medium">-$350.00</span>
                    </div>
                  </div>

                  <div className="p-4 border-b">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">Playground Maintenance</p>
                        <p className="text-sm text-gray-500">May 1, 2025</p>
                      </div>
                      <span className="text-red-500 font-medium">-$200.00</span>
                    </div>
                  </div>

                  <div className="flex justify-center">
                    <Button variant="ghost">
                      View All Transactions
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Balance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <p className="text-3xl font-bold">$0.00</p>
                  <p className="text-sm text-gray-500 mt-1">Available</p>
                </div>
              </CardContent>
              <CardFooter className="flex justify-center">
                <Button variant="outline" className="w-full">
                  Withdraw Funds
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="mr-2 h-5 w-5" />
                  Pending
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <p className="text-2xl font-bold">$0.00</p>
                  <p className="text-sm text-gray-500 mt-1">Processing</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Need Help?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm">
                  Having issues with payments or billing? Our support team is ready to help.
                </p>
              </CardContent>
              <CardFooter>
                <Button className="w-full">
                  Contact Support
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default Payments;
