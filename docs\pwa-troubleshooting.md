# PWA Troubleshooting Guide

This document provides guidance for troubleshooting issues with the ClassTasker Connect Progressive Web App (PWA), particularly focusing on GetStream chat functionality.

## Common Chrome Console Errors and Solutions

### 1. "Error: You can't use a channel after client.disconnect() was called"

**Cause**: This error occurs when the GetStream client is disconnected but the code still tries to use a channel.

**Solution**: We've implemented the following fixes:
- Modified the `disconnectUser` function to never disconnect the client in PWA mode
- Added enhanced connection handling with automatic reconnection
- Added periodic connection checks to ensure the client stays connected

### 2. Network Request Failures in PWA Mode

**Cause**: The PWA may have issues with relative URLs or service worker caching.

**Solution**: We've implemented the following fixes:
- Updated the service worker to better handle GetStream API requests
- Added multiple fallback URLs for token generation
- Added cache-busting headers to prevent caching of API requests
- Increased timeout durations for API requests in PWA mode

### 3. Environment Variable Access Issues

**Cause**: The PWA may have trouble accessing environment variables.

**Solution**: We've implemented the following fixes:
- Added fallback mechanisms to access environment variables from window.env
- Added detailed logging of environment variable access
- Added emergency fallback token for debugging purposes

## Debugging Steps

If you encounter issues with the PWA, follow these steps:

1. **Check Chrome Console Logs**:
   - Open Chrome DevTools (F12 or right-click > Inspect)
   - Go to the Console tab
   - Look for errors related to GetStream or service worker

2. **Check Network Requests**:
   - In Chrome DevTools, go to the Network tab
   - Filter for "getstream" or "stream.io"
   - Check if requests are failing and why

3. **Check Application Tab**:
   - In Chrome DevTools, go to the Application tab
   - Check Service Workers section to ensure the service worker is active
   - Check Cache Storage to see what's being cached

4. **Use the Debug Panel**:
   - Tap 5 times in the top-right corner of the PWA to open the debug panel
   - Use the "Test GetStream" button to test the connection
   - Check the logs for detailed information

## Advanced Debugging

For more advanced debugging, you can:

1. **Connect Chrome DevTools to Your Mobile Device**:
   - Connect your device to your computer via USB
   - Enable USB debugging on your device
   - Open Chrome on your computer and navigate to `chrome://inspect/#devices`
   - Click "inspect" next to your PWA

2. **Clear PWA Data**:
   - In Chrome DevTools > Application tab > Storage
   - Click "Clear site data" to reset the PWA
   - Reinstall the PWA after clearing data

3. **Update the Service Worker**:
   - In Chrome DevTools > Application tab > Service Workers
   - Check "Update on reload"
   - Reload the page to get the latest service worker

## Recent Fixes

We've recently implemented the following fixes to address PWA issues:

1. **Service Worker Improvements**:
   - Updated cache version to v12
   - Added more comprehensive patterns for non-cacheable requests
   - Improved error handling for API requests
   - Added longer timeouts for GetStream requests

2. **GetStream Client Improvements**:
   - Prevented disconnection in PWA mode
   - Added automatic reconnection on connection loss
   - Added periodic connection checks
   - Improved token generation with multiple fallbacks

3. **URL Handling Improvements**:
   - Added support for absolute URLs in PWA mode
   - Added fallback URLs for token generation
   - Added cache-busting headers to prevent caching

## Reporting Issues

If you continue to experience issues with the PWA, please provide the following information:

1. Device and browser information (e.g., iPhone 12, Chrome 91)
2. Steps to reproduce the issue
3. Screenshots of any error messages
4. Console logs from Chrome DevTools (if possible)

## PWA Version Information

The current PWA version is 1.5.1. You can check your installed version by looking at the version indicator at the bottom of the PWA home screen.
