import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  CheckCircle,
  Clock,
  AlertCircle,
  MessageSquare,
  Calendar,
  ChevronRight,
  WifiOff,
  RefreshCw
} from 'lucide-react';
import {
  isOnline,
  isPWA,
  getCachedUserTasks,
  getCachedChatThreads,
  getCachedDashboardData,
  registerConnectivityListeners,
  storeUserTasks,
  storeChatThreads,
  storeDashboardData
} from '@/utils/pwa-utils';
import { supabase } from '@/integrations/supabase/client';

const PWADashboard: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('tasks');
  const [tasks, setTasks] = useState<any[]>([]);
  const [messages, setMessages] = useState<any[]>([]);
  const [stats, setStats] = useState<any>({
    tasksCompleted: 0,
    tasksInProgress: 0,
    unreadMessages: 0,
    upcomingTasks: 0
  });
  const [loading, setLoading] = useState(true);
  const [offlineMode, setOfflineMode] = useState(!isOnline());
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Effect to handle online/offline status
  useEffect(() => {
    const cleanup = registerConnectivityListeners(
      // Online callback
      () => {
        setOfflineMode(false);
        fetchData(); // Refresh data when coming back online
      },
      // Offline callback
      () => {
        setOfflineMode(true);
      }
    );

    return cleanup;
  }, []);

  // Fetch data from API or cache
  const fetchData = async () => {
    setLoading(true);

    try {
      if (!user) return;

      // If offline and PWA, use cached data
      if (!isOnline() && isPWA()) {
        console.log('[PWADashboard] Using cached data in offline mode');

        // Get cached tasks
        const cachedTasks = getCachedUserTasks(user.id);
        if (cachedTasks && cachedTasks.length > 0) {
          setTasks(cachedTasks);
        }

        // Get cached messages
        const cachedThreads = getCachedChatThreads();
        if (cachedThreads && cachedThreads.length > 0) {
          setMessages(cachedThreads);
        }

        // Get cached dashboard data
        const cachedDashboard = getCachedDashboardData();
        if (cachedDashboard) {
          setStats(cachedDashboard.stats || {});
        }

        setLastUpdated(new Date());
      } else {
        console.log('[PWADashboard] Fetching live data for user:', user.id);

        // Fetch tasks from API - use correct field names
        const { data: tasksData, error: tasksError } = await supabase
          .from('tasks')
          .select('*')
          .or(`user_id.eq.${user.id},assigned_to.eq.${user.id}`)
          .order('created_at', { ascending: false })
          .limit(10);

        if (tasksError) {
          console.error('[PWADashboard] Error fetching tasks:', tasksError);
        } else {
          console.log('[PWADashboard] Fetched tasks:', tasksData?.length || 0);
          if (tasksData) {
            setTasks(tasksData);
            // Cache the tasks for offline use
            storeUserTasks(user.id, tasksData);
          }
        }

        // Fetch messages/chat threads
        try {
          const { data: threadsData, error: threadsError } = await supabase
            .from('chat_threads')
            .select('*, task:tasks(title)')
            .or(`supplier_id.eq.${user.id},admin_id.eq.${user.id}`)
            .order('updated_at', { ascending: false })
            .limit(10);

          if (threadsError) {
            console.error('[PWADashboard] Error fetching chat threads:', threadsError);
          } else {
            console.log('[PWADashboard] Fetched chat threads:', threadsData?.length || 0);
            if (threadsData) {
              // Format threads for display
              const formattedThreads = threadsData.map(thread => ({
                id: thread.id,
                name: thread.task?.title || `Task Discussion`,
                last_message: { content: 'Click to view messages', created_at: thread.updated_at },
                unread_count: 0
              }));
              setMessages(formattedThreads);
              // Cache the threads for offline use
              storeChatThreads(formattedThreads);
            }
          }
        } catch (threadError) {
          console.error('[PWADashboard] Error processing chat threads:', threadError);
        }

        // Calculate dashboard stats manually since the RPC function might not exist
        try {
          // Try the RPC function first
          const { data: statsData, error: statsError } = await supabase
            .rpc('get_user_dashboard_stats', { user_id_param: user.id });

          if (statsError) {
            console.error('[PWADashboard] Error fetching stats via RPC:', statsError);
            console.log('[PWADashboard] Calculating stats manually from tasks');

            // Calculate stats manually from tasks
            if (tasksData) {
              // Define status groups for easier filtering
              const completedStatuses = ['completed', 'confirmed', 'approved', 'finished'];
              const inProgressStatuses = ['assigned', 'in_progress', 'accepted'];

              // Make sure we handle null or undefined status values
              const calculatedStats = {
                tasksCompleted: tasksData.filter(t => t.status && completedStatuses.includes(t.status.toLowerCase())).length,
                tasksInProgress: tasksData.filter(t => t.status && inProgressStatuses.includes(t.status.toLowerCase())).length,
                unreadMessages: 0, // Can't calculate this without message data
                upcomingTasks: tasksData.filter(t => t.due_date && new Date(t.due_date) > new Date()).length
              };

              // Log the stats calculation for debugging
              console.log('[PWADashboard] Task statuses:', tasksData.map(t => t.status));
              console.log('[PWADashboard] Calculated stats:', calculatedStats);
              setStats(calculatedStats);

              // Cache the stats for offline use
              storeDashboardData({ stats: calculatedStats });
            } else {
              // If no tasks data, set default stats
              const defaultStats = {
                tasksCompleted: 0,
                tasksInProgress: 0,
                unreadMessages: 0,
                upcomingTasks: 0
              };
              setStats(defaultStats);
            }
          } else {
            console.log('[PWADashboard] Fetched stats via RPC:', statsData);
            if (statsData) {
              setStats(statsData);
              // Cache the stats for offline use
              storeDashboardData({ stats: statsData });
            } else {
              // If RPC returns null or undefined, calculate manually
              console.log('[PWADashboard] RPC returned no data, calculating manually');

              if (tasksData) {
                // Define status groups for easier filtering
                const completedStatuses = ['completed', 'confirmed', 'approved', 'finished'];
                const inProgressStatuses = ['assigned', 'in_progress', 'accepted'];

                // Make sure we handle null or undefined status values
                const calculatedStats = {
                  tasksCompleted: tasksData.filter(t => t.status && completedStatuses.includes(t.status.toLowerCase())).length,
                  tasksInProgress: tasksData.filter(t => t.status && inProgressStatuses.includes(t.status.toLowerCase())).length,
                  unreadMessages: 0,
                  upcomingTasks: tasksData.filter(t => t.due_date && new Date(t.due_date) > new Date()).length
                };

                // Log the stats calculation for debugging
                console.log('[PWADashboard] Task statuses (fallback):', tasksData.map(t => t.status));
                console.log('[PWADashboard] Calculated stats (fallback):', calculatedStats);
                setStats(calculatedStats);

                // Cache the stats for offline use
                storeDashboardData({ stats: calculatedStats });
              }
            }
          }
        } catch (statsError) {
          console.error('[PWADashboard] Error processing stats:', statsError);
        }

        setLastUpdated(new Date());
      }
    } catch (error) {
      console.error('[PWADashboard] Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    if (user) {
      fetchData();
    }
  }, [user]);

  // Render task item
  const renderTaskItem = (task: any) => (
    <Card key={task.id} className="mb-3 cursor-pointer hover:bg-gray-50" onClick={() => navigate(`/tasks/${task.id}`)}>
      <CardContent className="p-4">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-medium text-sm">{task.title}</h3>
            <p className="text-gray-500 text-xs mt-1">{task.description?.substring(0, 60)}...</p>
          </div>
          <Badge variant={getStatusVariant(task.status)}>{task.status}</Badge>
        </div>
        <div className="flex justify-between items-center mt-3">
          <div className="text-xs text-gray-500">
            {new Date(task.created_at).toLocaleDateString()}
          </div>
          <ChevronRight className="h-4 w-4 text-gray-400" />
        </div>
      </CardContent>
    </Card>
  );

  // Render message item
  const renderMessageItem = (thread: any) => (
    <div
      key={thread.id}
      className="px-4 py-3 cursor-pointer hover:bg-gray-50 transition-colors border-b border-gray-100"
      onClick={() => navigate(`/messages/${thread.id}`)}
    >
      <div className="flex items-center">
        <Avatar className="h-10 w-10 mr-3 flex-shrink-0">
          <AvatarFallback>{getInitials(thread.name || 'User')}</AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <div className="flex justify-between items-baseline">
            <h3 className="font-medium text-gray-900 truncate">{thread.name || 'Chat'}</h3>
            <span className="text-xs text-gray-400 ml-2 flex-shrink-0">
              {thread.last_message?.created_at
                ? new Date(thread.last_message.created_at).toLocaleDateString()
                : 'No activity'}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-500 truncate mt-1">
              {thread.last_message?.content?.substring(0, 40) || 'No messages yet'}...
            </p>
            {thread.unread_count > 0 && (
              <Badge variant="destructive" className="rounded-full px-2 py-0.5 ml-2 flex-shrink-0">
                {thread.unread_count}
              </Badge>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  // Helper function to get status variant
  const getStatusVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'approved':
      case 'finished':
        return 'default';
      case 'in progress':
      case 'accepted':
        return 'secondary';
      case 'pending':
      case 'offered':
        return 'outline';
      case 'rejected':
      case 'cancelled':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  // Helper function to get initials
  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <div className="container max-w-md mx-auto px-4 py-6">
      {/* Offline indicator */}
      {offlineMode && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4 flex items-center">
          <WifiOff className="h-5 w-5 text-yellow-500 mr-2" />
          <div className="text-sm text-yellow-700">
            You're offline. Some data may not be up to date.
          </div>
        </div>
      )}

      {/* Stats cards */}
      <div className="grid grid-cols-2 gap-3 mb-6">
        <Card>
          <CardContent className="p-4 flex flex-col items-center justify-center">
            <Clock className="h-6 w-6 text-blue-500 mb-2" />
            <p className="text-xs text-gray-500">In Progress</p>
            <p className="text-xl font-bold">{stats.tasksInProgress || 0}</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 flex flex-col items-center justify-center">
            <CheckCircle className="h-6 w-6 text-green-500 mb-2" />
            <p className="text-xs text-gray-500">Completed</p>
            <p className="text-xl font-bold">{stats.tasksCompleted || 0}</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 flex flex-col items-center justify-center">
            <MessageSquare className="h-6 w-6 text-purple-500 mb-2" />
            <p className="text-xs text-gray-500">Unread</p>
            <p className="text-xl font-bold">{stats.unreadMessages || 0}</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 flex flex-col items-center justify-center">
            <Calendar className="h-6 w-6 text-orange-500 mb-2" />
            <p className="text-xs text-gray-500">Upcoming</p>
            <p className="text-xl font-bold">{stats.upcomingTasks || 0}</p>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="tasks" value={activeTab} onValueChange={setActiveTab} className="mb-6">
        <TabsList className="grid grid-cols-2">
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
          <TabsTrigger value="messages">Messages</TabsTrigger>
        </TabsList>

        <TabsContent value="tasks" className="mt-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Recent Tasks</h2>
            <Button variant="outline" size="sm" onClick={fetchData} disabled={loading}>
              <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>

          {loading ? (
            Array(3).fill(0).map((_, i) => (
              <Card key={i} className="mb-3">
                <CardContent className="p-4">
                  <Skeleton className="h-5 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-full mb-3" />
                  <div className="flex justify-between">
                    <Skeleton className="h-4 w-1/4" />
                    <Skeleton className="h-4 w-4 rounded-full" />
                  </div>
                </CardContent>
              </Card>
            ))
          ) : tasks.length > 0 ? (
            tasks.map(renderTaskItem)
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">No tasks found</p>
                <Button variant="outline" className="mt-3" onClick={() => navigate('/tasks/create')}>
                  Create a Task
                </Button>
              </CardContent>
            </Card>
          )}

          <Button variant="default" className="w-full mt-3" onClick={() => navigate('/tasks')}>
            View All Tasks
          </Button>
        </TabsContent>

        <TabsContent value="messages" className="mt-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Recent Messages</h2>
            <Button variant="outline" size="sm" onClick={fetchData} disabled={loading}>
              <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>

          {loading ? (
            <div className="border rounded-lg overflow-hidden bg-white">
              {Array(3).fill(0).map((_, i) => (
                <React.Fragment key={i}>
                  <div className="px-4 py-3">
                    <div className="flex items-center">
                      <Skeleton className="h-10 w-10 rounded-full mr-3 flex-shrink-0" />
                      <div className="flex-1">
                        <div className="flex justify-between items-baseline">
                          <Skeleton className="h-5 w-32" />
                          <Skeleton className="h-4 w-16 ml-2" />
                        </div>
                        <Skeleton className="h-4 w-48 mt-1" />
                      </div>
                    </div>
                  </div>
                  {i < 2 && (
                    <div className="h-px bg-gray-100 mx-4" />
                  )}
                </React.Fragment>
              ))}
            </div>
          ) : messages.length > 0 ? (
            <div className="border rounded-lg overflow-hidden bg-white">
              {messages.map(renderMessageItem)}
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <MessageSquare className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">No messages found</p>
              </CardContent>
            </Card>
          )}

          <Button variant="default" className="w-full mt-3" onClick={() => navigate('/messages')}>
            View All Messages
          </Button>
        </TabsContent>
      </Tabs>

      {/* Last updated */}
      {lastUpdated && (
        <div className="text-xs text-center text-gray-500 mt-4">
          Last updated: {lastUpdated.toLocaleTimeString()}
        </div>
      )}
    </div>
  );
};

export default PWADashboard;
