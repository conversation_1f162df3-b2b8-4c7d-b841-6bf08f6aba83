// <PERSON>ript to create a test invitation
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import crypto from 'crypto';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createTestInvitation() {
  try {
    console.log('Creating a test invitation...');

    // Get the test user ID (we'll use this as the invited_by value)
    const testUserId = 'e0eb9971-8690-4b51-b0d6-04805f2955ab'; // The <NAME_EMAIL>

    // Get the test organization ID
    const { data: orgs, error: orgsError } = await supabase
      .from('organizations')
      .select('id')
      .eq('name', 'Test Organization')
      .limit(1);

    if (orgsError || !orgs || orgs.length === 0) {
      console.error('Error fetching organization:', orgsError);
      return;
    }

    const orgId = orgs[0].id;
    console.log(`Using organization ID: ${orgId}`);

    // Generate a random token
    const token = crypto.randomBytes(16).toString('hex');

    // Create the invitation with a unique email
    const uniqueEmail = `test.invitation.${Date.now()}@example.com`;
    console.log(`Using unique email: ${uniqueEmail}`);

    const { data: invitation, error: inviteError } = await supabase
      .from('user_invitations')
      .insert({
        email: uniqueEmail,
        organization_id: orgId,
        role: 'teacher',
        invited_by: testUserId,
        token: token,
        status: 'pending',
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days from now
      })
      .select()
      .single();

    if (inviteError) {
      console.error('Error creating invitation:', inviteError);
      return;
    }

    console.log('Test invitation created successfully!');
    console.log('Invitation details:');
    console.log(`ID: ${invitation.id}`);
    console.log(`Email: ${invitation.email}`);
    console.log(`Organization ID: ${invitation.organization_id}`);
    console.log(`Role: ${invitation.role}`);
    console.log(`Status: ${invitation.status}`);
    console.log(`Token: ${invitation.token}`);
    console.log(`Created: ${new Date(invitation.created_at).toLocaleString()}`);
    console.log(`Expires: ${new Date(invitation.expires_at).toLocaleString()}`);

    // Generate an invitation link
    const invitationLink = `${supabaseUrl.replace('https://', 'http://localhost:8082/')}/invitation/accept?token=${invitation.token}`;
    console.log(`\nInvitation Link: ${invitationLink}`);

    // Now test the accept_invitation function
    console.log('\nTesting accept_invitation function with the new invitation...');

    // Use the existing test user to accept the invitation
    // We already have testUserId from above

    // Accept the invitation
    const { data: acceptResult, error: acceptError } = await supabase
      .rpc('accept_invitation', {
        token_param: invitation.token,
        user_id_param: testUserId
      });

    if (acceptError) {
      console.error('Error accepting invitation:', acceptError);
      return;
    }

    console.log(`Invitation accepted: ${acceptResult}`);

    // Check if the user's profile was updated
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', testUserId)
      .single();

    if (profileError) {
      console.error('Error fetching profile:', profileError);
      return;
    }

    console.log('\nUpdated user profile:');
    console.log(`ID: ${profile.id}`);
    console.log(`Organization ID: ${profile.organization_id}`);
    console.log(`Role: ${profile.role}`);

    // Check if the invitation status was updated
    const { data: updatedInvite, error: inviteUpdateError } = await supabase
      .from('user_invitations')
      .select('status')
      .eq('id', invitation.id)
      .single();

    if (inviteUpdateError) {
      console.error('Error checking invitation status:', inviteUpdateError);
      return;
    }

    console.log(`\nInvitation status is now: ${updatedInvite.status}`);

    console.log('\nTest completed successfully!');
  } catch (error) {
    console.error('Error creating test invitation:', error);
  }
}

createTestInvitation();
