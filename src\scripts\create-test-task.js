// <PERSON>ript to create a test task
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createTestTask() {
  try {
    console.log('Creating a test task...');
    
    // Get a user from the database to use as the task creator
    const { data: users, error: usersError } = await supabase
      .from('profiles')
      .select('id, role')
      .eq('role', 'teacher')
      .limit(1);
    
    if (usersError) {
      console.error('Error getting users:', usersError);
      return;
    }
    
    if (!users || users.length === 0) {
      console.log('No teacher users found, trying to get any user...');
      
      const { data: anyUsers, error: anyUsersError } = await supabase
        .from('profiles')
        .select('id, role')
        .limit(1);
      
      if (anyUsersError) {
        console.error('Error getting any users:', anyUsersError);
        return;
      }
      
      if (!anyUsers || anyUsers.length === 0) {
        console.error('No users found in the database');
        return;
      }
      
      users[0] = anyUsers[0];
    }
    
    const userId = users[0].id;
    const userRole = users[0].role;
    console.log(`Using user ${userId} with role ${userRole} as the task creator`);
    
    // Create a test task
    const testTask = {
      title: 'Test Task Created by Script',
      description: 'This is a test task created by the script',
      location: 'Test Location',
      category: 'Test Category',
      budget: 100,
      due_date: new Date().toISOString(),
      status: 'open',
      visibility: 'admin',
      user_id: userId
    };
    
    console.log('Creating task with data:', testTask);
    
    const { data, error } = await supabase
      .from('tasks')
      .insert([testTask])
      .select();
    
    if (error) {
      console.error('Error creating test task:', error);
      return;
    }
    
    console.log('Successfully created test task:', data[0]);
    
  } catch (error) {
    console.error('Error creating test task:', error);
  }
}

createTestTask();
