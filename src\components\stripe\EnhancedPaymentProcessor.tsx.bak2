import { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import './payment-processor.css'; // Import custom styles
import {
  Elements,
  PaymentElement,
  useStripe,
  useElements,
  AddressElement,
  LinkAuthenticationElement
} from '@stripe/react-stripe-js';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, AlertCircle, CheckCircle2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { stripeService } from '@/services/stripeService';
import { supabase } from '@/integrations/supabase/client';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Initialize Stripe
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

// Define appearance options for Stripe Elements
const appearance = {
  theme: 'stripe',
  variables: {
    colorPrimary: '#0570de',
    colorBackground: '#ffffff',
    colorText: '#30313d',
    colorDanger: '#df1b41',
    fontFamily: 'system-ui, sans-serif',
    spacingUnit: '4px',
    borderRadius: '8px',
    fontSizeBase: '15px',
  },
  rules: {
    '.Tab': {
      border: '1px solid #e6e6e6',
      boxShadow: '0px 1px 1px rgba(0, 0, 0, 0.03)',
      marginBottom: '8px',
      padding: '8px 12px',
    },
    '.Tab:hover': {
      color: 'var(--colorText)',
      backgroundColor: '#f9f9f9',
    },
    '.Tab--selected': {
      borderColor: '#0570de',
      boxShadow: '0px 1px 1px rgba(0, 0, 0, 0.03)',
      backgroundColor: '#f6f8fa',
    },
    '.Input--invalid': {
      boxShadow: '0 1px 1px 0 rgba(0, 0, 0, 0.07), 0 0 0 2px var(--colorDanger)',
    },
    '.Label': {
      fontSize: '14px',
      fontWeight: '500',
      marginBottom: '6px',
    },
    '.TabIcon': {
      marginRight: '8px',
    },
    '.TabLabel': {
      fontSize: '15px',
    },
    // Make form elements more compact
    '.FormRow': {
      marginBottom: '12px',
    },
  },
};

// Check if we're on a production domain with HTTPS
const isProduction = window.location.protocol === 'https:' && !window.location.hostname.includes('localhost');

// Define payment method order based on environment
const paymentMethodOrder = [
  'card',
  'paypal',
  'revolut_pay',
  'bacs_debit',
];

// Only include Apple Pay and Google Pay on production domains with HTTPS
if (isProduction) {
  paymentMethodOrder.push('apple_pay');
  paymentMethodOrder.push('google_pay');
}

// Define options for the payment element
const paymentElementOptions = {
  layout: {
    type: 'accordion',
    defaultCollapsed: false,
    spacedAccordionItems: false,
  },
  paymentMethodOrder,
  defaultValues: {
    billingDetails: {
      name: '',
      email: '',
      phone: '',
      address: {
        country: 'GB',
      },
    },
  },
  business: {
    name: 'Class Tasker Connect',
  },
};

interface EnhancedPaymentProcessorProps {
  taskId: string;
  offerId: string;
  amount: number;
  onSuccess?: () => void;
  onCancel?: () => void;
}

// The wrapper component that provides the Stripe Elements context
export default function EnhancedPaymentProcessor(props: EnhancedPaymentProcessorProps) {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [paymentId, setPaymentId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    // Create a payment record and get a client secret when the component mounts
    const createPayment = async () => {
      try {
        setLoading(true);
        setError(null);

        if (!user) {
          throw new Error('User not authenticated');
        }

        // Create a payment record
        const payment = await stripeService.createPaymentWithDirectTransfer(
          props.taskId,
          props.offerId,
          props.amount
        );

        if (!payment) {
          throw new Error('Failed to create payment record');
        }

        setPaymentId(payment.id);

        // Get a client secret for the payment
        const secret = await stripeService.createPaymentIntent(payment.id);

        if (!secret) {
          throw new Error('Failed to create payment intent');
        }

        setClientSecret(secret);
      } catch (err) {
        console.error('Error creating payment:', err);
        setError('Failed to initialize payment. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    createPayment();
  }, [props.taskId, props.offerId, props.amount, user]);

  if (loading) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Initializing Payment</CardTitle>
          <CardDescription>
            Please wait while we set up your payment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-row items-center justify-center py-4 gap-6">
            <Loader2 className="h-12 w-12 animate-spin text-gray-500 flex-shrink-0" />
            <div>
              <p className="text-gray-700">
                Please wait while we prepare your payment options. This will only take a moment.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Payment Error</CardTitle>
          <CardDescription>
            There was a problem setting up your payment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-row items-center justify-center py-4 gap-6">
            <AlertCircle className="h-12 w-12 text-red-500 flex-shrink-0" />
            <div>
              <h3 className="text-lg font-medium mb-2 text-red-600">Payment Error</h3>
              <p className="text-gray-700">{error}</p>
              <p className="text-gray-500 mt-2 text-sm">Please try again or contact support if the problem persists.</p>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={props.onCancel} className="w-full">
            Go Back
          </Button>
        </CardFooter>
      </Card>
    );
  }

  if (!clientSecret) {
    return null;
  }

  return (
    <Elements
      stripe={stripePromise}
      options={{
        clientSecret,
        appearance
      }}
    >
      <PaymentForm
        taskId={props.taskId}
        amount={props.amount}
        onSuccess={props.onSuccess}
        onCancel={props.onCancel}
      />
    </Elements>
  );
}

// The actual payment form component
function PaymentForm({
  taskId,
  amount,
  onSuccess,
  onCancel
}: {
  taskId: string;
  amount: number;
  onSuccess?: () => void;
  onCancel?: () => void;
}) {
  const { user } = useAuth();
  const stripe = useStripe();
  const elements = useElements();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<string>('card');

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Confirm the payment
      const { error: submitError, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: window.location.origin + '/payment-confirmation',
          payment_method_data: {
            billing_details: {
              name: user?.user_metadata?.full_name || 'Unknown',
              email: user?.email || undefined,
            },
          },
          // Save payment method for future use},
        redirect: 'if_required',
      });

      if (submitError) {
        throw new Error(submitError.message);
      }

      if (paymentIntent?.status === 'succeeded' || paymentIntent?.status === 'processing') {
        setSuccess(true);

        // Update the task status
        await supabase
          .from('tasks')
          .update({ payment_status: 'processing' })
          .eq('id', taskId);

        // Call the onSuccess callback
        if (onSuccess) {
          onSuccess();
        }
      } else {
        throw new Error('Payment failed');
      }
    } catch (err) {
      console.error('Error processing payment:', err);
      setError(err instanceof Error ? err.message : 'Payment failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Payment Successful</CardTitle>
          <CardDescription>
            Your payment has been processed successfully
          </CardDescription>
        </CardHeader>

        <CardContent>
          <div className="flex flex-row items-center justify-center py-4 gap-6">
            <CheckCircle2 className="h-16 w-16 text-green-500 flex-shrink-0" />
            <div>
              <h3 className="text-lg font-medium mb-2">Payment Successful</h3>
              <p className="text-gray-700">
                Thank you for your payment. The supplier will be notified and will begin work on your task.
              </p>
            </div>
          </div>
        </CardContent>

        <CardFooter>
          <Button onClick={onSuccess} className="w-full">
            Return to Task
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Complete Payment</CardTitle>
        <CardDescription>
          Pay for your task to release funds to the supplier
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} id="payment-form">
          <div className="flex flex-col space-y-6">
            {/* Payment amount and email in a single row */}
            <div className="flex flex-col sm:flex-row gap-6 justify-between">
              <div>
                <div className="text-sm font-medium mb-2">Payment Amount</div>
                <div className="text-2xl font-bold">£{amount.toFixed(2)}</div>
              </div>

              <div className="w-full sm:w-1/2">
                <div className="text-sm font-medium mb-2">Email</div>
                <LinkAuthenticationElement
                  options={{
                    defaultValues: {
                      email: user?.email || '',
                    },
                  }}
                />
              </div>
            </div>

            {/* Payment method - full width */}
            <div>
              <div className="text-sm font-medium mb-2">Payment Method</div>
              <div className="payment-element-container">
                <PaymentElement options={paymentElementOptions} />
              </div>
            </div>

            {/* Billing address - full width */}
            <div>
              <div className="text-sm font-medium mb-2">Billing Address</div>
              <AddressElement
                options={{
                  mode: 'billing',
                  fields: {
                    phone: 'always',
                  },
                  defaultValues: {
                    name: user?.user_metadata?.full_name || '',
                    address: {
                      line1: user?.user_metadata?.address?.line1 || '',
                      line2: user?.user_metadata?.address?.line2 || '',
                      city: user?.user_metadata?.address?.city || '',
                      state: user?.user_metadata?.address?.state || '',
                      postal_code: user?.user_metadata?.address?.postal_code || '',
                      country: user?.user_metadata?.address?.country || 'GB',
                    },
                  },
                }}
              />
            </div>

            {/* Error messages */}
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </div>
        </form>
      </CardContent>

      <CardFooter className="flex flex-col gap-2">
        <Button
          onClick={handleSubmit}
          disabled={loading || !stripe || !elements}
          className="w-full"
        >
          {loading ? (
            <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing...</>
          ) : (
            `Pay £${amount.toFixed(2)}`
          )}
        </Button>

        <Button
          variant="outline"
          onClick={onCancel}
          disabled={loading}
          className="w-full"
        >
          Cancel
        </Button>
      </CardFooter>
    </Card>
  );
}
