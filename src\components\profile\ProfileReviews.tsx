
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Star } from 'lucide-react';

interface Review {
  id: string;
  author: string;
  role: string;
  rating: number;
  date: string;
  content: string;
}

interface ProfileReviewsProps {
  reviews: Review[];
}

const ProfileReviews = ({ reviews }: ProfileReviewsProps) => {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold">Reviews</h3>
          <div className="flex items-center bg-blue-50 px-3 py-1 rounded-full">
            <Star size={16} className="text-yellow-500 mr-1" />
            <span className="font-medium mr-1">4.8</span>
            <span className="text-gray-600 text-sm">(12 reviews)</span>
          </div>
        </div>
        
        <div className="space-y-6">
          {reviews.map((review) => (
            <div key={review.id} className="border-b border-gray-100 pb-6 last:border-b-0 last:pb-0">
              <div className="flex justify-between items-start">
                <div className="flex items-center">
                  <Avatar className="h-10 w-10 mr-3">
                    <AvatarFallback>
                      {review.author.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{review.author}</p>
                    <p className="text-sm text-gray-600">{review.role}</p>
                  </div>
                </div>
                <div className="flex">
                  {Array(5).fill(0).map((_, i) => (
                    <Star 
                      key={i} 
                      size={16} 
                      className={i < review.rating ? "text-yellow-500" : "text-gray-300"} 
                      fill={i < review.rating ? "currentColor" : "none"} 
                    />
                  ))}
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                {new Date(review.date).toLocaleDateString()}
              </p>
              <p className="mt-3 text-gray-700">{review.content}</p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileReviews;
