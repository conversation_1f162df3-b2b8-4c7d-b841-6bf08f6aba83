import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import {
  Search,
  ClipboardList,
  MessageSquare,
  Plus,
  User,
  WifiOff,
  RefreshCw,
  Bell
} from 'lucide-react';
import { isOnline, isPWA, registerConnectivityListeners } from '@/utils/pwa-utils';
import { useNotifications } from '@/contexts/NotificationContext';

interface PWAMobileLayoutProps {
  children: React.ReactNode;
  hideBottomNav?: boolean; // Optional prop to hide bottom navigation
  headerAction?: React.ReactNode; // Optional action button for header (like filter)
}

const PWAMobileLayout: React.FC<PWAMobileLayoutProps> = ({ children, hideBottomNav = false, headerAction }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();

  // Auto-detect if we should hide bottom nav based on current route
  const shouldHideBottomNav = hideBottomNav ||
    location.pathname.startsWith('/messages/') || // Individual chat screens
    location.pathname.startsWith('/chat/') ||     // Alternative chat routes
    location.pathname.includes('/chat-view/');    // Chat view routes

  const { unreadCount } = useNotifications();

  const [offlineMode, setOfflineMode] = useState(!isOnline());
  const [showOfflineBanner, setShowOfflineBanner] = useState(!isOnline());

  // Handle notifications button click
  const handleNotificationsClick = () => {
    navigate('/notifications');
  };

  // Effect to handle online/offline status
  useEffect(() => {
    const cleanup = registerConnectivityListeners(
      // Online callback
      () => {
        setOfflineMode(false);
        // Show online notification briefly
        setShowOfflineBanner(false);
      },
      // Offline callback
      () => {
        setOfflineMode(true);
        setShowOfflineBanner(true);
      }
    );

    return cleanup;
  }, []);

  // Hide offline banner after 5 seconds
  useEffect(() => {
    if (showOfflineBanner) {
      const timer = setTimeout(() => {
        setShowOfflineBanner(false);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [showOfflineBanner]);

  // Check if the current route is active
  const isActive = (path: string): boolean => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  // Get active class for nav items
  const getActiveClass = (path: string): string => {
    return isActive(path)
      ? 'text-primary'
      : 'text-gray-500';
  };

  // Handle refresh
  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-4 py-3 sticky top-0 z-10">
        <div className="flex justify-between items-center">
          <h1 className="text-lg font-semibold">ClassTasker</h1>

          {/* Show refresh button in offline mode */}
          {offlineMode ? (
            <Button variant="ghost" size="sm" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-1" />
              Refresh
            </Button>
          ) : (
            <div className="flex items-center space-x-2">
              {/* Optional header action (like filter button) */}
              {headerAction}

              {user && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="relative rounded-full hover:bg-gray-100 active:bg-gray-200 transition-colors"
                  onClick={handleNotificationsClick}
                  style={{ minWidth: '40px', minHeight: '40px' }}
                >
                  <Bell className="h-5 w-5 text-gray-700" />
                  {unreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] text-white font-medium">
                      {unreadCount > 9 ? '9+' : unreadCount}
                    </span>
                  )}
                </Button>
              )}
            </div>
          )}
        </div>
      </header>

      {/* Offline banner */}
      {showOfflineBanner && (
        <div className="bg-yellow-50 border-b border-yellow-200 px-4 py-2 flex items-center justify-center">
          <WifiOff className="h-4 w-4 text-yellow-500 mr-2" />
          <span className="text-xs text-yellow-700">
            {offlineMode
              ? "You're offline. Some features may be limited."
              : "You're back online!"}
          </span>
        </div>
      )}

      {/* Main content */}
      <main className="flex-1 overflow-auto">
        {children}
      </main>

      {/* Bottom navigation - hidden in chat screens for better UX */}
      {!shouldHideBottomNav && (
        <nav className="bg-white border-t border-gray-200 px-4 py-2 sticky bottom-0 z-10">
          <div className="flex justify-between items-center">
            <Button
              variant="ghost"
              size="icon"
              className={`flex flex-col items-center hover:bg-transparent focus-visible:bg-transparent active:bg-transparent ${getActiveClass('/marketplace')}`}
              onClick={() => navigate('/marketplace')}
            >
              <Search className="h-5 w-5" />
              <span className="text-xs mt-1">Search</span>
            </Button>

            <Button
              variant="ghost"
              size="icon"
              className={`flex flex-col items-center hover:bg-transparent focus-visible:bg-transparent active:bg-transparent ${getActiveClass('/tasks')}`}
              onClick={() => navigate('/tasks')}
            >
              <ClipboardList className="h-5 w-5" />
              <span className="text-xs mt-1">Tasks</span>
            </Button>

            <Button
              variant="default"
              size="icon"
              className="flex flex-col items-center rounded-full h-12 w-12 shadow-lg"
              onClick={() => navigate('/tasks/create')}
            >
              <Plus className="h-6 w-6" />
            </Button>

            <Button
              variant="ghost"
              size="icon"
              className={`flex flex-col items-center hover:bg-transparent focus-visible:bg-transparent active:bg-transparent ${getActiveClass('/messages')}`}
              onClick={() => navigate('/messages')}
            >
              <MessageSquare className="h-5 w-5" />
              <span className="text-xs mt-1">Chat</span>
            </Button>

            <Button
              variant="ghost"
              size="icon"
              className={`flex flex-col items-center hover:bg-transparent focus-visible:bg-transparent active:bg-transparent ${getActiveClass('/profile')}`}
              onClick={() => navigate('/profile')}
            >
              <User className="h-5 w-5" />
              <span className="text-xs mt-1">Profile</span>
            </Button>
          </div>
        </nav>
      )}
    </div>
  );
};

export default PWAMobileLayout;
