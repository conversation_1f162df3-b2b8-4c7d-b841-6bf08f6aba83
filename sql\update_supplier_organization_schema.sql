-- SQL migration to update the organizations table to properly support supplier organizations
-- This script ensures that 'supplier' is a valid organization_type and adds any necessary constraints

-- First, add missing columns to the profiles table for supplier business information
ALTER TABLE public.profiles
ADD COLUMN IF NOT EXISTS business_description TEXT,
ADD COLUMN IF NOT EXISTS services TEXT,
ADD COLUMN IF NOT EXISTS business_name TEXT;

-- First, check if we need to modify the organization_type column to accept 'supplier'
DO $$
BEGIN
  -- Check if there's a constraint on organization_type that would prevent 'supplier'
  IF EXISTS (
    SELECT 1 FROM pg_constraint
    WHERE conname = 'organizations_organization_type_check'
  ) THEN
    -- Drop the existing constraint
    ALTER TABLE organizations DROP CONSTRAINT organizations_organization_type_check;

    -- Add a new constraint that includes 'supplier'
    ALTER TABLE organizations
    ADD CONSTRAINT organizations_organization_type_check
    CHECK (organization_type IN ('school', 'trust', 'supplier'));

    RAISE NOTICE 'Updated organization_type constraint to include supplier';
  ELSE
    -- No constraint exists, so we can just add one
    ALTER TABLE organizations
    ADD CONSTRAINT organizations_organization_type_check
    CHECK (organization_type IN ('school', 'trust', 'supplier'));

    RAISE NOTICE 'Added new organization_type constraint including supplier';
  END IF;
END
$$;

-- Add a function to create a supplier organization and link it to a profile
CREATE OR REPLACE FUNCTION create_supplier_organization(
  name_param TEXT,
  user_id_param UUID,
  address_param TEXT DEFAULT NULL,
  city_param TEXT DEFAULT NULL,
  postcode_param TEXT DEFAULT NULL,
  phone_param TEXT DEFAULT NULL,
  website_param TEXT DEFAULT NULL,
  business_description_param TEXT DEFAULT NULL,
  services_param TEXT DEFAULT NULL
)
RETURNS UUID
SECURITY DEFINER
AS $$
DECLARE
  org_id UUID;
BEGIN
  -- Insert the new supplier organization
  INSERT INTO public.organizations (
    name,
    organization_type,
    address,
    city,
    zip,
    phone,
    website,
    created_at,
    updated_at
  )
  VALUES (
    name_param,
    'supplier',
    address_param,
    city_param,
    postcode_param,
    phone_param,
    website_param,
    NOW(),
    NOW()
  )
  RETURNING id INTO org_id;

  -- Update the supplier's profile to link to this organization
  UPDATE public.profiles
  SET
    organization_id = org_id,
    business_description = business_description_param,
    services = services_param
  WHERE id = user_id_param;

  RETURN org_id;
END;
$$ LANGUAGE plpgsql;

-- Create a policy to allow suppliers to view and update their own organization
DO $$
BEGIN
  -- Check if the policy already exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE policyname = 'Suppliers can manage their own organization'
  ) THEN
    CREATE POLICY "Suppliers can manage their own organization"
    ON public.organizations
    FOR ALL
    TO authenticated
    USING (
      id IN (
        SELECT organization_id FROM public.profiles
        WHERE id = auth.uid() AND account_type = 'supplier'
      )
    );

    RAISE NOTICE 'Created policy for suppliers to manage their own organization';
  ELSE
    RAISE NOTICE 'Policy for suppliers to manage their own organization already exists';
  END IF;
END
$$;

-- Update the types in the application
COMMENT ON TABLE public.organizations IS 'Organizations table for schools, trusts, and supplier businesses';
COMMENT ON COLUMN public.organizations.organization_type IS 'Type of organization: school, trust, or supplier';
