// Service Worker for ClassTasker Connect PWA
const CACHE_NAME = 'classtasker-cache-v13';
const OFFLINE_URL = '/offline.html';
const PWA_VERSION = '1.5.2'; // Update this when making changes to the PWA

// Resources to cache immediately on install
const PRECACHE_RESOURCES = [
  '/',
  '/index.html',
  '/offline.html',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png',
  '/icons/dashboard.png',
  '/icons/tasks.png',
  '/icons/messages.png',
  '/icons/post-task.png'
];

// Resources to cache when requested
const DYNAMIC_CACHE_PATTERNS = [
  /\.(?:js|css|woff2)$/
];

// API endpoints that should NOT be cached
const NO_CACHE_PATTERNS = [
  // Never cache any GetStream API requests
  /\/api\/getstream\//,
  /\/api\/getstream-token/,
  // Never cache Stream Chat API requests
  /stream-chat-api/,
  /getstream\.io/,
  /stream-chat-temporary/,
  /stream\.io/,
  // Never cache Supabase API requests
  /supabase/,
  // Never cache any API requests
  /\/api\//
];

// Install event - cache core resources
self.addEventListener('install', (event) => {
  console.log('[Service Worker] Installing Service Worker...');

  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('[Service Worker] Precaching resources');
        return cache.addAll(PRECACHE_RESOURCES);
      })
      .then(() => {
        console.log('[Service Worker] Successfully installed');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('[Service Worker] Precaching failed:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[Service Worker] Activating Service Worker...');

  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME) {
              console.log('[Service Worker] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[Service Worker] Service Worker activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', (event) => {
  // Skip cross-origin requests
  if (!event.request.url.startsWith(self.location.origin)) {
    return;
  }

  // Skip non-GET requests
  if (event.request.method !== 'GET') {
    return;
  }

  // Handle API requests differently
  if (event.request.url.includes('/api/')) {
    handleApiRequest(event);
    return;
  }

  // For navigation requests, use network-first strategy
  if (event.request.mode === 'navigate') {
    event.respondWith(
      fetch(event.request)
        .catch(() => {
          return caches.match(OFFLINE_URL);
        })
    );
    return;
  }

  // For static assets, use cache-first strategy
  const shouldCache = DYNAMIC_CACHE_PATTERNS.some(pattern =>
    pattern.test(event.request.url)
  );

  if (shouldCache) {
    event.respondWith(
      caches.match(event.request)
        .then((cachedResponse) => {
          if (cachedResponse) {
            return cachedResponse;
          }

          return fetch(event.request)
            .then((response) => {
              // Don't cache non-successful responses
              if (!response || response.status !== 200) {
                return response;
              }

              // Clone the response to cache it and return it
              const responseToCache = response.clone();
              caches.open(CACHE_NAME)
                .then((cache) => {
                  cache.put(event.request, responseToCache);
                })
                .catch(err => {
                  console.error('[Service Worker] Error caching response:', err);
                });

              return response;
            })
            .catch(() => {
              // If both cache and network fail, return offline page for HTML
              if (event.request.headers.get('Accept') &&
                  event.request.headers.get('Accept').includes('text/html')) {
                return caches.match(OFFLINE_URL);
              }

              // For other types of requests, return a simple error response
              const contentType = event.request.headers.get('Accept') || 'text/plain';
              if (contentType.includes('application/json')) {
                return new Response(JSON.stringify({ error: 'Network error occurred' }), {
                  status: 408,
                  headers: { 'Content-Type': 'application/json' }
                });
              } else {
                return new Response('Network error occurred', {
                  status: 408,
                  headers: { 'Content-Type': 'text/plain' }
                });
              }
            });
        })
    );
    return;
  }

  // For other requests, use network-first strategy
  event.respondWith(
    fetch(event.request)
      .catch(() => {
        return caches.match(event.request)
          .then(cachedResponse => {
            if (cachedResponse) {
              return cachedResponse;
            }
            // If no cache match, return a simple offline response
            if (event.request.headers.get('Accept').includes('text/html')) {
              return caches.match(OFFLINE_URL);
            }
            return new Response('Network error occurred', {
              status: 408,
              headers: { 'Content-Type': 'text/plain' }
            });
          });
      })
  );
});

// Handle API requests
function handleApiRequest(event) {
  // Check if this is a GetStream API request that should not be cached
  const shouldNotCache = NO_CACHE_PATTERNS.some(pattern =>
    pattern.test(event.request.url)
  );

  // Check if this is specifically a GetStream request
  const isGetStreamRequest = event.request.url.includes('getstream') ||
                            event.request.url.includes('stream.io');

  if (shouldNotCache) {
    console.log('[Service Worker] Handling non-cacheable API request:', event.request.url);

    // For GetStream API requests, always go to network and don't cache
    // Use a longer timeout for GetStream requests
    const timeoutDuration = isGetStreamRequest ? 10000 : 5000;

    event.respondWith(
      Promise.race([
        fetch(event.request.clone()),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Request timeout')), timeoutDuration)
        )
      ])
      .then(response => {
        // Just return the response, don't clone it to avoid memory issues
        return response;
      })
      .catch((error) => {
        console.error('[Service Worker] Error for API request:', event.request.url, error);

        // For GetStream requests, return a specific error format they expect
        if (isGetStreamRequest) {
          return new Response(JSON.stringify({
            error: 'Network error occurred',
            details: error.message || 'Could not connect to GetStream API',
            url: event.request.url
          }), {
            status: 503,
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            }
          });
        }

        // For other API requests
        return new Response(JSON.stringify({
          error: 'Network error occurred',
          details: error.message || 'Could not connect to API server',
          url: event.request.url
        }), {
          status: 503,
          headers: { 'Content-Type': 'application/json' }
        });
      })
    );
  } else {
    // For other API requests, use network-first with caching
    event.respondWith(
      fetch(event.request)
        .then((response) => {
          // Don't cache non-successful responses
          if (!response || response.status !== 200) {
            return response;
          }

          // Clone the response to cache it and return it
          const responseToCache = response.clone();
          caches.open(CACHE_NAME)
            .then((cache) => {
              cache.put(event.request, responseToCache);
            })
            .catch(err => {
              console.error('[Service Worker] Error caching API response:', err);
            });

          return response;
        })
        .catch(() => {
          // For API requests, try to return from cache if available
          return caches.match(event.request)
            .then(cachedResponse => {
              if (cachedResponse) {
                return cachedResponse;
              }
              // If no cache match, return a simple error response
              return new Response(JSON.stringify({ error: 'Network error occurred' }), {
                status: 408,
                headers: { 'Content-Type': 'application/json' }
              });
            });
        })
    );
  }
}

// Listen for messages from clients
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});
