/**
 * API Server for Local Development
 *
 * This server serves the API routes locally during development.
 * It's designed to mimic the behavior of Vercel's serverless functions.
 */

import express from 'express';
import cors from 'cors';
import { StreamChat } from 'stream-chat';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get API key and secret from environment variables
const apiKey = process.env.GETSTREAM_API_KEY || process.env.VITE_GETSTREAM_API_KEY;
const apiSecret = process.env.GETSTREAM_API_SECRET;

// Check if API key and secret are available
if (!apiKey || !apiSecret) {
  console.error('Error: GetStream API key or secret is missing in environment variables.');
  console.error('Make sure VITE_GETSTREAM_API_KEY and GETSTREAM_API_SECRET are set in your .env file.');
  process.exit(1);
}

// Create a server-side client for GetStream
const serverClient = StreamChat.getInstance(apiKey, apiSecret);

// Create an Express app
const app = express();
const PORT = 3002;

// Enable CORS for all routes with more permissive settings
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['*'], // Allow all headers
  exposedHeaders: ['*'], // Expose all headers
  credentials: true,
  maxAge: 86400 // Cache preflight requests for 24 hours
}));

// Add OPTIONS handling for preflight requests
app.options('/*', cors());

// Add CORS headers to all responses
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', '*');
  res.header('Access-Control-Expose-Headers', '*');
  res.header('Access-Control-Allow-Credentials', 'true');
  next();
});

// Parse JSON request bodies
app.use(express.json());

// API Routes

// Generate a token for a user
app.post('/api/getstream/token', async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    console.log('Generating token for user:', userId);

    // Generate a token for the user
    const token = serverClient.createToken(userId);

    console.log('Token generated successfully');

    res.json({ token });
  } catch (error) {
    console.error('Error generating token:', error);
    res.status(500).json({ error: 'Failed to generate token' });
  }
});

// Create a channel for a task
app.post('/api/getstream/channels', async (req, res) => {
  try {
    const { taskId, taskTitle, members } = req.body;

    if (!taskId || !taskTitle) {
      return res.status(400).json({ error: 'Task ID and title are required' });
    }

    console.log('Creating channel for task:', taskId);

    // Create a channel
    const channelId = `task-${taskId}`;

    // Use provided members or create an empty array
    const channelMembers = members && Array.isArray(members) ? members : [];

    try {
      // First try to get the channel if it exists
      const channel = serverClient.channel('messaging', channelId);
      await channel.query();

      // If channel exists, update members
      if (channelMembers.length > 0) {
        // Add members to the channel
        await channel.addMembers(channelMembers);
        console.log(`Added ${channelMembers.length} members to existing channel:`, channelId);
      }

      console.log('Channel already exists:', channelId);

      res.json({
        channelId,
        channel: channel.id,
        members: channel.state.members,
        status: 'updated'
      });
    } catch (error) {
      // If channel doesn't exist, create it
      console.log('Channel does not exist, creating new channel');

      // Create users first if they don't exist
      for (const memberId of channelMembers) {
        try {
          // Create or update the user in GetStream
          await serverClient.upsertUser({
            id: memberId,
            name: memberId, // Use the ID as the name for simplicity
          });
          console.log(`Created/updated user ${memberId} in GetStream`);
        } catch (userError) {
          console.error(`Error creating user ${memberId} in GetStream:`, userError);
        }
      }

      // Create a new channel
      const channel = serverClient.channel('messaging', channelId, {
        name: taskTitle,
        members: channelMembers,
        task_id: taskId,
        created_by_id: 'system' // Add this field to fix the error
      });

      await channel.create();

      console.log('Channel created successfully:', channelId);

      res.json({
        channelId,
        channel: channel.id,
        members: channel.state.members,
        status: 'created'
      });
    }
  } catch (error) {
    console.error('Error creating/updating channel:', error);
    res.status(500).json({ error: 'Failed to create/update channel' });
  }
});

// Send a system message to a channel
app.post('/api/getstream/system-message', async (req, res) => {
  try {
    const { channelId, text } = req.body;

    if (!channelId || !text) {
      return res.status(400).json({ error: 'Channel ID and text are required' });
    }

    console.log('Sending system message to channel:', channelId);

    // Get the channel
    const channel = serverClient.channel('messaging', channelId);

    // Send the system message
    const message = await channel.sendMessage({
      text,
      type: 'system',
      user_id: 'system',
    });

    console.log('System message sent successfully:', message);

    res.json({ success: true, message });
  } catch (error) {
    console.error('Error sending system message:', error);
    res.status(500).json({ error: 'Failed to send system message' });
  }
});

// Create a user in GetStream
app.post('/api/getstream/create-user', async (req, res) => {
  try {
    const { userId, userName } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    console.log('Creating user in GetStream:', userId);

    // Create or update the user in GetStream
    await serverClient.upsertUser({
      id: userId,
      name: userName || userId,
    });

    console.log('User created successfully:', userId);

    res.json({ success: true, userId });
  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({ error: 'Failed to create user' });
  }
});

// Add members to a channel
app.post('/api/getstream/members/:channelId', async (req, res) => {
  try {
    const { channelId } = req.params;
    const { members } = req.body;

    if (!channelId) {
      return res.status(400).json({ error: 'Channel ID is required' });
    }

    if (!members || !Array.isArray(members) || members.length === 0) {
      return res.status(400).json({ error: 'Members array is required' });
    }

    console.log(`Adding ${members.length} members to channel:`, channelId);

    try {
      // Get the channel
      const channel = serverClient.channel('messaging', channelId);

      // Add members to the channel
      const result = await channel.addMembers(members);

      console.log('Members added successfully to channel:', channelId);

      res.json({
        channelId,
        members: result.members,
        status: 'updated'
      });
    } catch (error) {
      console.error('Error adding members to channel:', error);
      res.status(500).json({ error: 'Failed to add members to channel' });
    }
  } catch (error) {
    console.error('Error adding members to channel:', error);
    res.status(500).json({ error: 'Failed to add members to channel' });
  }
});

// Simple route for testing
app.get('/', (req, res) => {
  res.json({
    status: 'API Server is running',
    timestamp: new Date().toISOString(),
    clientInfo: {
      ip: req.ip,
      headers: req.headers,
      userAgent: req.get('user-agent')
    }
  });
});

// Add a test endpoint for mobile connectivity testing
app.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'API server is reachable from your device',
    timestamp: new Date().toISOString(),
    clientInfo: {
      ip: req.ip,
      userAgent: req.get('user-agent')
    }
  });
});

// Start the server
app.listen(PORT, () => {
  console.log(`API Server listening on port ${PORT}`);
  console.log(`API Key: ${apiKey}`);
  console.log(`API Secret: ${apiSecret.substring(0, 5)}...${apiSecret.substring(apiSecret.length - 5)}`);
});
