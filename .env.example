# Supabase Configuration
# Get these from: https://supabase.com/dashboard/project/qcnotlojmyvpqbbgoxbc/settings/api

# Supabase Project URL (frontend)
VITE_SUPABASE_URL=https://qcnotlojmyvpqbbgoxbc.supabase.co

# Supabase Anon/Public Key (frontend - safe to expose)
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Supabase Service Role Key (backend only - NEVER expose in frontend)
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# GetStream Configuration
# Get this from: https://getstream.io/dashboard/

# GetStream API Key (for chat functionality)
VITE_GETSTREAM_API_KEY=your_getstream_api_key_here

# Google Maps Configuration
# Get this from: https://console.cloud.google.com/apis/credentials

# Google Maps API Key (for location features)
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Supabase Functions URL (optional - defaults to project URL + /functions/v1)
VITE_SUPABASE_FUNCTIONS_URL=https://qcnotlojmyvpqbbgoxbc.supabase.co/functions/v1

# Email Configuration (Resend)
# Get this from: https://resend.com/api-keys

# Resend API Key (for sending emails via Edge Functions)
RESEND_API_KEY=your_resend_api_key_here

# Stripe Configuration
# Get these from: https://dashboard.stripe.com/apikeys

# Stripe Public Key (frontend - safe to expose)
VITE_STRIPE_PUBLIC_KEY=your_stripe_public_key_here

# Stripe Secret Key (backend only - NEVER expose in frontend)
STRIPE_SECRET_KEY=your_stripe_secret_key_here

# Stripe Webhook Secret (for webhook signature verification)
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# Development Notes:
# 1. Copy this file to .env.local for local development
# 2. Never commit .env.local to version control
# 3. Set these same variables in your Vercel project settings
# 4. Variables with VITE_ prefix are bundled into the frontend
# 5. Variables without VITE_ prefix are server-side only
