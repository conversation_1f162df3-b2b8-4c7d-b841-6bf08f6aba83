/**
 * GetStream Chat Client Verification Test
 *
 * This script verifies that the GetStream chat client can be initialized
 * with the correct API key. It doesn't attempt to connect a user or
 * create channels, as that requires server-generated tokens.
 *
 * Run this script with Node.js to verify the GetStream client initialization:
 * node src/tests/getstream-test.js
 */

// Import required modules
import { StreamChat } from 'stream-chat';

// Get API key from environment variables
const apiKey = process.env.VITE_GETSTREAM_API_KEY || process.env.GETSTREAM_API_KEY;

console.log('Starting GetStream Chat Client Verification Test');
console.log('-----------------------------------------------');
console.log(`Using GetStream API key: ${apiKey}`);

// Initialize the client
try {
  const client = StreamChat.getInstance(apiKey);
  console.log('✅ StreamChat client initialized successfully');

  // Verify client properties
  console.log('Client properties:');
  console.log(`- API Key: ${client.key}`);
  console.log(`- Base URL: ${client.baseURL}`);
  console.log(`- Connection ID: ${client.connectionID || 'Not connected'}`);

  console.log('-----------------------------------------------');
  console.log('GetStream Chat Client Verification Test completed successfully');

  // Note about token generation
  console.log('\nNOTE: This test only verifies client initialization.');
  console.log('For full functionality, the application requires server-generated tokens.');
  console.log('The PWA implementation uses the token server at /api/getstream/token.');

} catch (error) {
  console.error('❌ Test failed with error:', error);
  process.exit(1);
}
