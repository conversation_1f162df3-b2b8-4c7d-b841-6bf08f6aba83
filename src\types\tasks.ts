/**
 * Task Type System for ClassTasker
 *
 * This file defines the task type interfaces and type guards for the application.
 * It implements a clear separation between internal and external tasks.
 */

/**
 * Base task interface with common properties for all task types
 */
export interface BaseTask {
  id: string;
  title: string;
  description: string;
  location: string;
  location_formatted?: string;
  location_lat?: number;
  location_lng?: number;
  location_place_id?: string;
  building?: string;
  room?: string;
  category: string;
  budget: number;
  due_date: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  organization_id?: string;
  organization_name?: string;
  assigned_to_name?: string;
  creator_name?: string;
  images?: string[]; // Array of image URLs
}

/**
 * Internal task assigned to school staff
 */
export interface InternalTask extends BaseTask {
  type: 'internal';
  visibility: 'internal'; // For backward compatibility
  status: 'assigned' | 'in_progress' | 'completed' | 'confirmed';
  assigned_to: string; // ID of internal staff member
  assigned_role: string; // Role of the assigned staff (maintenance, support, IT, etc.)
  payment_status: 'not_required';
  offers_count: number; // Always 0 for internal tasks
}

/**
 * External task for marketplace
 */
export interface ExternalTask extends BaseTask {
  type: 'external';
  visibility: 'public' | 'admin'; // For backward compatibility
  status: 'open' | 'interest' | 'questions' | 'offer' | 'assigned' | 'in_progress' | 'completed' | 'confirmed' | 'pending_payment';
  assigned_to?: string | null; // ID of supplier (if assigned)
  payment_status?: 'pending' | 'processing' | 'paid' | 'not_required';
  offers_count: number;
}

/**
 * Union type for application use
 */
export type Task = InternalTask | ExternalTask;

/**
 * Type guard to check if a task is an internal task
 */
export function isInternalTask(task: Task): task is InternalTask {
  return task.type === 'internal';
}

/**
 * Type guard to check if a task is an external task
 */
export function isExternalTask(task: Task): task is ExternalTask {
  return task.type === 'external';
}

/**
 * Type guard to check if a task is pending admin review
 */
export function isPendingReviewTask(task: Task): boolean {
  return task.type === 'pending' || (task.visibility === 'admin' && task.status === 'open');
}

/**
 * Type for creating a new task (omitting auto-generated fields)
 */
export type NewTask = Omit<BaseTask, 'id' | 'created_at' | 'updated_at'> & {
  type?: 'internal' | 'external' | 'pending';
  visibility?: 'admin' | 'internal' | 'public';
  status?: string;
  assigned_to?: string;
  assigned_role?: string;
  payment_status?: 'pending' | 'processing' | 'paid' | 'not_required';
  images?: string[]; // Array of image URLs
};

/**
 * Status definitions for internal tasks
 */
export const INTERNAL_TASK_STATUSES = [
  'assigned',
  'in_progress',
  'completed',
  'confirmed'
] as const;

/**
 * Status definitions for external tasks
 */
export const EXTERNAL_TASK_STATUSES = [
  'open',
  'interest',
  'questions',
  'offer',
  'assigned',
  'in_progress',
  'completed',
  'confirmed',
  'pending_payment'
] as const;

/**
 * Type for internal task status
 */
export type InternalTaskStatus = typeof INTERNAL_TASK_STATUSES[number];

/**
 * Type for external task status
 */
export type ExternalTaskStatus = typeof EXTERNAL_TASK_STATUSES[number];

/**
 * Function to get the appropriate status array based on task type
 */
export function getStatusesForTaskType(type: 'internal' | 'external'): readonly string[] {
  return type === 'internal' ? INTERNAL_TASK_STATUSES : EXTERNAL_TASK_STATUSES;
}

/**
 * Function to check if a status is valid for a given task type
 */
export function isValidStatusForTaskType(status: string, type: 'internal' | 'external'): boolean {
  return getStatusesForTaskType(type).includes(status);
}
