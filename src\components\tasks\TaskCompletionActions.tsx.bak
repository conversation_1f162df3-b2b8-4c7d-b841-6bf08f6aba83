import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Loader2, CheckCircle, AlertCircle, FileText } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { Task, Offer } from '@/services/taskService';
import { stripeService } from '@/services/stripeService';
import EnhancedPaymentProcessor from '@/components/stripe/EnhancedPaymentProcessor';

interface TaskCompletionActionsProps {
  task: Task;
  acceptedOffer: Offer | null;
  onTaskUpdated: () => void;
}

const TaskCompletionActions = ({ task, acceptedOffer, onTaskUpdated }: TaskCompletionActionsProps) => {
  const { toast } = useToast();
  const [isMarkingComplete, setIsMarkingComplete] = useState(false);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);

  // Only show this component for task owners with assigned tasks
  if (!task || task.status !== 'assigned') {
    return null;
  }

  // Debug info
  console.log('TaskCompletionActions:', {
    taskStatus: task.status,
    acceptedOffer,
    offers: acceptedOffer ? 'Has accepted offer' : 'No accepted offer'
  });

  // Check if there's an accepted offer
  if (!acceptedOffer) {
    return null;
  }

  const handleMarkComplete = async () => {
    try {
      setIsMarkingComplete(true);

      // Update the task status to pending_payment
      const { error } = await supabase
        .from('tasks')
        .update({
          status: 'pending_payment',
          payment_status: 'pending'
        })
        .eq('id', task.id);

      if (error) {
        throw error;
      }

      // Create a payment record for the task
      if (acceptedOffer) {
        const payment = await stripeService.createPaymentWithDirectTransfer(
          task.id,
          acceptedOffer.id,
          Number(acceptedOffer.amount)
        );

        if (payment) {
          // Generate an invoice for the payment
          try {
            const invoice = await stripeService.createInvoice(payment.id);
            if (invoice) {
              console.log(`Invoice ${invoice.invoice_number} created successfully`);
              toast({
                title: "Invoice Generated",
                description: "An invoice has been generated and sent to your email.",
                variant: "default",
              });
            }
          } catch (invoiceError) {
            console.error('Error generating invoice:', invoiceError);
            // Continue even if invoice generation fails
          }
        }
      }

      toast({
        title: "Task marked as ready for payment",
        description: "You can now complete the payment to finalize this task.",
        variant: "default",
      });

      // Show payment dialog
      setShowPaymentDialog(true);

      // Refresh task data
      onTaskUpdated();
    } catch (error) {
      console.error('Error marking task as complete:', error);
      toast({
        title: "Error",
        description: "Failed to mark task as complete. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsMarkingComplete(false);
    }
  };

  const handlePaymentSuccess = () => {
    setShowPaymentDialog(false);
    toast({
      title: "Payment successful",
      description: "Your payment has been processed successfully. The task is now complete.",
      variant: "default",
    });
    onTaskUpdated();
  };

  const handlePaymentCancel = () => {
    setShowPaymentDialog(false);
  };

  return (
    <>
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Task Completion</CardTitle>
          <CardDescription>
            Mark this task as complete when the work has been finished to your satisfaction
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            onClick={handleMarkComplete}
            className="w-full bg-green-600 hover:bg-green-700"
            disabled={isMarkingComplete}
          >
            {isMarkingComplete ? (
              <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing...</>
            ) : (
              <><CheckCircle className="mr-2 h-4 w-4" /> Mark as Complete & Pay</>
            )}
          </Button>
        </CardContent>
      </Card>

      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="sm:max-w-[800px] w-[90vw]">
          <DialogHeader>
            <DialogTitle>Complete Payment</DialogTitle>
            <DialogDescription>
              Pay for the completed task to release funds to the supplier.
            </DialogDescription>
          </DialogHeader>

          {acceptedOffer && (
            <EnhancedPaymentProcessor
              taskId={task.id}
              offerId={acceptedOffer.id}
              amount={Number(acceptedOffer.amount)}
              onSuccess={handlePaymentSuccess}
              onCancel={handlePaymentCancel}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default TaskCompletionActions;
