# Task Flow Test Script

This script tests the complete task flow in ClassTasker, including:
- Creating a new task
- Submitting an offer as a supplier
- Accepting the offer as an admin
- Marking the task as in progress
- Uploading an image to the chat
- Marking the task as completed
- Approving the task
- Processing payment

## Setup and Running

### Option 1: Run All Steps Automatically

Run the all-in-one script that will guide you through all steps:
```
node run-all.js
```

This script will:
1. Install dependencies
2. Get user IDs
3. Prompt you to update the .env file
4. Create the test image
5. Run the test

### Option 2: Manual Setup

1. Install dependencies:
   ```
   npm install
   ```

2. Update the `.env` file with your Supabase credentials:
   ```
   VITE_SUPABASE_URL=your-supabase-url
   VITE_SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   ```

3. Get the user IDs for testing:
   ```
   node get-user-ids.js
   ```

   This will output the user IDs for the admin and supplier accounts. Update your `.env` file with these IDs:
   ```
   ADMIN_USER_ID=admin-user-id-from-output
   SUPPLIER_USER_ID=supplier-user-id-from-output
   ```

4. Create the test image:
   ```
   node create-test-image.js
   ```

5. Run the test script:
   ```
   npm test
   ```

The script will output progress to the console and create a new task with all the associated data in your Supabase database.

## Viewing the Results

After running the test, you can view the created task in the ClassTasker UI by navigating to:
```
http://localhost:8082/tasks/{task-id}
```

Replace `{task-id}` with the task ID output by the script.

## Cleanup

To clean up the test data, you can manually delete the created task and associated data from your Supabase database.
