
import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import {
  Wrench,
  Calendar,
  Shield,
  CheckCircle,
  Users,
  Building,
  Globe,
  PoundSterling,
  ClipboardList,
  ArrowRight,
  BarChart,
  Bell
} from 'lucide-react';

const HowItWorks = () => {
  return (
    <MainLayout>
      <div className="container mx-auto py-12 px-4">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="inline-block px-3 py-1 bg-classtasker-blue/10 text-classtasker-blue rounded-full text-sm font-medium mb-4">
            Complete School Management Solution
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">How ClassTasker Works</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            ClassTasker is a comprehensive platform for schools to manage internal tasks, external contractors, and compliance requirements - all in one place.
          </p>
        </div>

        {/* Main Features */}
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4 mb-16">
          <Card className="shadow-md border-t-4 border-classtasker-blue">
            <CardHeader>
              <CardTitle className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-classtasker-blue/10 text-classtasker-blue flex items-center justify-center mr-3">
                  <Building className="h-5 w-5" />
                </div>
                Internal Task Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4">Assign maintenance tasks to your internal staff, track progress, and ensure timely completion.</p>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                  <span>Create and assign tasks to specific team members</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                  <span>Track progress with status updates</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                  <span>Prioritize urgent maintenance needs</span>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card className="shadow-md border-t-4 border-classtasker-orange">
            <CardHeader>
              <CardTitle className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-classtasker-orange/10 text-classtasker-orange flex items-center justify-center mr-3">
                  <Globe className="h-5 w-5" />
                </div>
                External Contractor Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4">Find, hire, and manage qualified external contractors for specialized maintenance tasks.</p>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                  <span>Post tasks and receive competitive offers</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                  <span>Review contractor profiles and ratings</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                  <span>Secure payment processing</span>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card className="shadow-md border-t-4 border-classtasker-green">
            <CardHeader>
              <CardTitle className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-classtasker-green/10 text-classtasker-green flex items-center justify-center mr-3">
                  <Shield className="h-5 w-5" />
                </div>
                Compliance Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4">Track and manage all your compliance requirements with automated reminders and documentation.</p>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                  <span>Schedule recurring compliance tasks</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                  <span>Store documentation for audits</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                  <span>Receive automated reminders</span>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card className="shadow-md border-t-4 border-classtasker-purple">
            <CardHeader>
              <CardTitle className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-classtasker-purple/10 text-classtasker-purple flex items-center justify-center mr-3">
                  <BarChart className="h-5 w-5" />
                </div>
                Reporting & Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4">Generate comprehensive reports and gain insights into your maintenance operations.</p>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                  <span>Track spending and budgets</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                  <span>Monitor compliance completion rates</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                  <span>Export reports for audits</span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* How It Works Tabs */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-8">Platform Features</h2>

          <Tabs defaultValue="internal" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="internal">Internal Tasks</TabsTrigger>
              <TabsTrigger value="external">External Tasks</TabsTrigger>
              <TabsTrigger value="compliance">Compliance</TabsTrigger>
              <TabsTrigger value="team">Team Management</TabsTrigger>
            </TabsList>

            <TabsContent value="internal" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl">Internal Task Management</CardTitle>
                  <CardDescription>
                    Efficiently manage tasks assigned to your internal maintenance staff
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-xl font-semibold mb-4">How It Works</h3>
                      <ol className="space-y-4">
                        <li className="flex">
                          <span className="w-6 h-6 rounded-full bg-classtasker-blue text-white flex items-center justify-center mr-3 flex-shrink-0">1</span>
                          <div>
                            <p className="font-medium">Create Internal Tasks</p>
                            <p className="text-gray-600 text-sm mt-1">Teachers and staff can create maintenance requests that are automatically routed to administrators.</p>
                          </div>
                        </li>
                        <li className="flex">
                          <span className="w-6 h-6 rounded-full bg-classtasker-blue text-white flex items-center justify-center mr-3 flex-shrink-0">2</span>
                          <div>
                            <p className="font-medium">Assign to Staff</p>
                            <p className="text-gray-600 text-sm mt-1">Administrators can assign tasks to specific maintenance staff members based on skills and availability.</p>
                          </div>
                        </li>
                        <li className="flex">
                          <span className="w-6 h-6 rounded-full bg-classtasker-blue text-white flex items-center justify-center mr-3 flex-shrink-0">3</span>
                          <div>
                            <p className="font-medium">Track Progress</p>
                            <p className="text-gray-600 text-sm mt-1">Monitor task status updates, completion times, and any issues that arise during the process.</p>
                          </div>
                        </li>
                      </ol>
                    </div>
                    <div className="bg-gray-50 p-6 rounded-lg">
                      <h3 className="text-xl font-semibold mb-4">Key Features</h3>
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <ClipboardList className="h-5 w-5 text-classtasker-blue mr-3 mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium">Task Prioritization</p>
                            <p className="text-gray-600 text-sm mt-1">Set priority levels for urgent maintenance needs.</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <Bell className="h-5 w-5 text-classtasker-blue mr-3 mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium">Automated Notifications</p>
                            <p className="text-gray-600 text-sm mt-1">Staff receive alerts when tasks are assigned to them.</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <Calendar className="h-5 w-5 text-classtasker-blue mr-3 mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium">Scheduling Tools</p>
                            <p className="text-gray-600 text-sm mt-1">Plan maintenance work around school hours and events.</p>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="external" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl">External Contractor Management</CardTitle>
                  <CardDescription>
                    Find and hire qualified contractors for specialized maintenance tasks
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-xl font-semibold mb-4">How It Works</h3>
                      <ol className="space-y-4">
                        <li className="flex">
                          <span className="w-6 h-6 rounded-full bg-classtasker-orange text-white flex items-center justify-center mr-3 flex-shrink-0">1</span>
                          <div>
                            <p className="font-medium">Post Your Task</p>
                            <p className="text-gray-600 text-sm mt-1">Describe what you need done, when you need it, and your budget for external contractors.</p>
                          </div>
                        </li>
                        <li className="flex">
                          <span className="w-6 h-6 rounded-full bg-classtasker-orange text-white flex items-center justify-center mr-3 flex-shrink-0">2</span>
                          <div>
                            <p className="font-medium">Review Offers</p>
                            <p className="text-gray-600 text-sm mt-1">Compare offers from qualified contractors, check reviews, and select the best match.</p>
                          </div>
                        </li>
                        <li className="flex">
                          <span className="w-6 h-6 rounded-full bg-classtasker-orange text-white flex items-center justify-center mr-3 flex-shrink-0">3</span>
                          <div>
                            <p className="font-medium">Simplified Payment</p>
                            <p className="text-gray-600 text-sm mt-1">Pay ClassTasker using your preferred method (including BACS) within 15 days upon invoice. We handle all contractor payments.</p>
                          </div>
                        </li>
                      </ol>
                    </div>
                    <div className="bg-gray-50 p-6 rounded-lg">
                      <h3 className="text-xl font-semibold mb-4">Key Features</h3>
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <Shield className="h-5 w-5 text-classtasker-orange mr-3 mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium">Verified Contractors</p>
                            <p className="text-gray-600 text-sm mt-1">All contractors are vetted and verified for quality and reliability.</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <PoundSterling className="h-5 w-5 text-classtasker-orange mr-3 mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium">Flexible Payment Options</p>
                            <p className="text-gray-600 text-sm mt-1">Pay using your preferred methods including BACS with 15-day payment terms upon invoice.</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <Users className="h-5 w-5 text-classtasker-orange mr-3 mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium">Contractor Ratings</p>
                            <p className="text-gray-600 text-sm mt-1">See reviews and ratings from other schools to make informed decisions.</p>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="compliance" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl">Compliance Management</CardTitle>
                  <CardDescription>
                    Track and manage all your compliance requirements for audits
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-xl font-semibold mb-4">How It Works</h3>
                      <ol className="space-y-4">
                        <li className="flex">
                          <span className="w-6 h-6 rounded-full bg-classtasker-green text-white flex items-center justify-center mr-3 flex-shrink-0">1</span>
                          <div>
                            <p className="font-medium">Set Up Compliance Tasks</p>
                            <p className="text-gray-600 text-sm mt-1">Create recurring compliance tasks with specific frequencies (daily, weekly, monthly, annually).</p>
                          </div>
                        </li>
                        <li className="flex">
                          <span className="w-6 h-6 rounded-full bg-classtasker-green text-white flex items-center justify-center mr-3 flex-shrink-0">2</span>
                          <div>
                            <p className="font-medium">Complete and Document</p>
                            <p className="text-gray-600 text-sm mt-1">Mark tasks as completed and upload supporting documentation for audit purposes.</p>
                          </div>
                        </li>
                        <li className="flex">
                          <span className="w-6 h-6 rounded-full bg-classtasker-green text-white flex items-center justify-center mr-3 flex-shrink-0">3</span>
                          <div>
                            <p className="font-medium">Monitor Compliance Status</p>
                            <p className="text-gray-600 text-sm mt-1">Track compliance rates and receive alerts for overdue or upcoming tasks.</p>
                          </div>
                        </li>
                      </ol>
                    </div>
                    <div className="bg-gray-50 p-6 rounded-lg">
                      <h3 className="text-xl font-semibold mb-4">Key Features</h3>
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <Calendar className="h-5 w-5 text-classtasker-green mr-3 mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium">Recurring Task Scheduling</p>
                            <p className="text-gray-600 text-sm mt-1">Set up daily, weekly, monthly, and annual compliance checks.</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <Bell className="h-5 w-5 text-classtasker-green mr-3 mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium">Automated Reminders</p>
                            <p className="text-gray-600 text-sm mt-1">Receive email notifications for upcoming and overdue compliance tasks.</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <BarChart className="h-5 w-5 text-classtasker-green mr-3 mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium">Compliance Reporting</p>
                            <p className="text-gray-600 text-sm mt-1">Generate comprehensive reports for audits and inspections.</p>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="team" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl">Team Management</CardTitle>
                  <CardDescription>
                    Manage your staff and external contractors with role-based permissions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-xl font-semibold mb-4">How It Works</h3>
                      <ol className="space-y-4">
                        <li className="flex">
                          <span className="w-6 h-6 rounded-full bg-classtasker-purple text-white flex items-center justify-center mr-3 flex-shrink-0">1</span>
                          <div>
                            <p className="font-medium">Add Team Members</p>
                            <p className="text-gray-600 text-sm mt-1">Invite staff members to join your organization on ClassTasker.</p>
                          </div>
                        </li>
                        <li className="flex">
                          <span className="w-6 h-6 rounded-full bg-classtasker-purple text-white flex items-center justify-center mr-3 flex-shrink-0">2</span>
                          <div>
                            <p className="font-medium">Assign Roles</p>
                            <p className="text-gray-600 text-sm mt-1">Set appropriate roles and permissions for administrators, teachers, and maintenance staff.</p>
                          </div>
                        </li>
                        <li className="flex">
                          <span className="w-6 h-6 rounded-full bg-classtasker-purple text-white flex items-center justify-center mr-3 flex-shrink-0">3</span>
                          <div>
                            <p className="font-medium">Manage Workloads</p>
                            <p className="text-gray-600 text-sm mt-1">Monitor task assignments and balance workloads across your team.</p>
                          </div>
                        </li>
                      </ol>
                    </div>
                    <div className="bg-gray-50 p-6 rounded-lg">
                      <h3 className="text-xl font-semibold mb-4">Key Features</h3>
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <Users className="h-5 w-5 text-classtasker-purple mr-3 mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium">Role-Based Permissions</p>
                            <p className="text-gray-600 text-sm mt-1">Control access to different parts of the platform based on user roles.</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <ClipboardList className="h-5 w-5 text-classtasker-purple mr-3 mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium">Task Assignment</p>
                            <p className="text-gray-600 text-sm mt-1">Easily assign tasks to specific team members or departments.</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <BarChart className="h-5 w-5 text-classtasker-purple mr-3 mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium">Performance Tracking</p>
                            <p className="text-gray-600 text-sm mt-1">Monitor task completion rates and response times for your team.</p>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* FAQ Section */}
        <div className="mt-16">
          <h2 className="text-3xl font-bold text-center mb-8">Frequently Asked Questions</h2>

          <div className="grid gap-6 md:grid-cols-2">
            <Card className="shadow-md">
              <CardHeader>
                <CardTitle>What types of tasks can I manage?</CardTitle>
              </CardHeader>
              <CardContent>
                <p>ClassTasker supports both internal and external task management. You can manage routine maintenance tasks with your internal staff, assign specialized work to external contractors, and track compliance requirements for audits - all in one platform.</p>
              </CardContent>
            </Card>

            <Card className="shadow-md">
              <CardHeader>
                <CardTitle>How does compliance tracking work?</CardTitle>
              </CardHeader>
              <CardContent>
                <p>Our compliance management system allows you to set up recurring tasks (daily, weekly, monthly, annually), track completion status, upload documentation for audits, and receive automated reminders for upcoming deadlines. This ensures you stay on top of all regulatory requirements.</p>
              </CardContent>
            </Card>

            <Card className="shadow-md">
              <CardHeader>
                <CardTitle>How do payments work for external contractors?</CardTitle>
              </CardHeader>
              <CardContent>
                <p>All payments are made to ClassTasker using your school's preferred payment methods, including BACS, within 15 days upon invoice. We support batch payment processing and manage all supplier payments on your behalf, ensuring contractors are paid promptly once work is completed satisfactorily. This streamlined approach simplifies your financial processes while protecting both schools and contractors.</p>
              </CardContent>
            </Card>

            <Card className="shadow-md">
              <CardHeader>
                <CardTitle>Can I manage multiple schools or locations?</CardTitle>
              </CardHeader>
              <CardContent>
                <p>Yes, ClassTasker supports multi-academy trusts and organizations with multiple locations. Administrators can manage tasks, compliance, and team members across all sites from a single dashboard, with detailed reporting for each location.</p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <h2 className="text-2xl font-bold mb-4">Ready to streamline your school maintenance?</h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Join hundreds of schools already using ClassTasker to manage internal tasks, external contractors, and compliance requirements.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button asChild size="lg" className="bg-classtasker-blue hover:bg-blue-600">
              <Link to="/register">Join Now</Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link to="/contact">Contact Sales</Link>
            </Button>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default HowItWorks;
