import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  ListTodo, 
  PlusCircle, 
  Search, 
  Clock, 
  AlertCircle 
} from 'lucide-react';

interface DashboardHeaderProps {
  userName?: string;
  isTeacher: boolean;
  isAdmin: boolean;
  isMaintenance: boolean;
  isSupplier: boolean;
  pendingReviewCount: number;
  urgentTasksCount: number;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  userName,
  isTeacher,
  isAdmin,
  isMaintenance,
  isSupplier,
  pendingReviewCount,
  urgentTasksCount
}) => {
  // Get time of day for greeting
  const hour = new Date().getHours();
  let greeting = 'Good evening';
  
  if (hour < 12) {
    greeting = 'Good morning';
  } else if (hour < 18) {
    greeting = 'Good afternoon';
  }

  // Determine primary action based on user role
  let primaryAction = null;
  let secondaryAction = null;

  if (isTeacher) {
    primaryAction = (
      <Button className="bg-classtasker-blue hover:bg-blue-600" asChild>
        <Link to="/post-task">
          <PlusCircle className="mr-2 h-4 w-4" />
          Create New Task
        </Link>
      </Button>
    );
  } else if (isAdmin) {
    if (pendingReviewCount > 0) {
      primaryAction = (
        <Button className="bg-classtasker-blue hover:bg-blue-600" asChild>
          <Link to="/dashboard?tab=my-tasks">
            <AlertCircle className="mr-2 h-4 w-4" />
            Review Pending Tasks ({pendingReviewCount})
          </Link>
        </Button>
      );
    } else {
      primaryAction = (
        <Button className="bg-classtasker-blue hover:bg-blue-600" asChild>
          <Link to="/post-task">
            <PlusCircle className="mr-2 h-4 w-4" />
            Create New Task
          </Link>
        </Button>
      );
    }
    
    secondaryAction = (
      <Button variant="outline" className="ml-2" asChild>
        <Link to="/organization/dashboard">
          <ListTodo className="mr-2 h-4 w-4" />
          Organization Dashboard
        </Link>
      </Button>
    );
  } else if (isMaintenance) {
    primaryAction = (
      <Button className="bg-classtasker-blue hover:bg-blue-600" asChild>
        <Link to="/dashboard?tab=my-jobs">
          <ListTodo className="mr-2 h-4 w-4" />
          View My Assigned Tasks
        </Link>
      </Button>
    );
  } else if (isSupplier) {
    primaryAction = (
      <Button className="bg-classtasker-blue hover:bg-blue-600" asChild>
        <Link to="/tasks">
          <Search className="mr-2 h-4 w-4" />
          Find Available Tasks
        </Link>
      </Button>
    );
  }

  return (
    <Card className="mb-8 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-100">
      <CardContent className="p-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 mb-1">
              {greeting}, {userName || 'there'}!
            </h1>
            <p className="text-gray-600 mb-4 md:mb-0">
              {isTeacher && 'Create and manage your tasks from your dashboard.'}
              {isAdmin && 'Manage your organization and review tasks from your dashboard.'}
              {isMaintenance && 'View and complete your assigned tasks from your dashboard.'}
              {isSupplier && 'Find new opportunities and manage your jobs from your dashboard.'}
            </p>
            
            {urgentTasksCount > 0 && (
              <div className="flex items-center text-amber-600 mt-2 text-sm font-medium">
                <Clock className="h-4 w-4 mr-1" />
                You have {urgentTasksCount} {urgentTasksCount === 1 ? 'task' : 'tasks'} due soon
              </div>
            )}
          </div>
          
          <div className="mt-4 md:mt-0 flex">
            {primaryAction}
            {secondaryAction}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DashboardHeader;
