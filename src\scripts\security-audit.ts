/**
 * Security Audit Script
 *
 * This script performs comprehensive security checks to ensure organization-level
 * data isolation is properly implemented across the application.
 */

import { createClient } from '@supabase/supabase-js';

import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL!;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY!;

console.log('Environment check:');
console.log('VITE_SUPABASE_URL:', supabaseUrl ? 'Set' : 'Missing');
console.log('VITE_SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? 'Set' : 'Missing');

const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface SecurityIssue {
  severity: 'critical' | 'high' | 'medium' | 'low';
  category: string;
  description: string;
  table?: string;
  recommendation: string;
}

class SecurityAuditor {
  private issues: SecurityIssue[] = [];

  private addIssue(issue: SecurityIssue) {
    this.issues.push(issue);
    console.log(`[${issue.severity.toUpperCase()}] ${issue.category}: ${issue.description}`);
  }

  async auditRLSPolicies() {
    console.log('\n🔍 Auditing RLS Policies...');

    // Check if RLS is enabled on critical tables
    const criticalTables = ['tasks', 'profiles', 'task_messages', 'task_comments', 'organizations'];

    for (const table of criticalTables) {
      const { data, error } = await supabase
        .from('pg_tables')
        .select('*')
        .eq('tablename', table)
        .eq('schemaname', 'public');

      if (error) {
        this.addIssue({
          severity: 'high',
          category: 'RLS Check',
          description: `Could not verify RLS status for table: ${table}`,
          table,
          recommendation: 'Ensure table exists and RLS is properly configured'
        });
        continue;
      }

      // Check if RLS is enabled (this requires a custom query)
      const { data: rlsData } = await supabase.rpc('exec_sql', {
        sql: `SELECT relrowsecurity FROM pg_class WHERE relname = '${table}' AND relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')`
      });

      if (!rlsData || rlsData.length === 0 || !rlsData[0].relrowsecurity) {
        this.addIssue({
          severity: 'critical',
          category: 'RLS Missing',
          description: `Row Level Security is not enabled on table: ${table}`,
          table,
          recommendation: `Enable RLS with: ALTER TABLE public.${table} ENABLE ROW LEVEL SECURITY;`
        });
      }
    }
  }

  async auditOrganizationColumns() {
    console.log('\n🔍 Auditing Organization ID Columns...');

    const tablesNeedingOrgId = ['tasks', 'profiles', 'task_messages', 'task_comments'];

    for (const table of tablesNeedingOrgId) {
      const { data, error } = await supabase.rpc('exec_sql', {
        sql: `SELECT column_name FROM information_schema.columns WHERE table_name = '${table}' AND column_name = 'organization_id' AND table_schema = 'public'`
      });

      if (error || !data || data.length === 0) {
        this.addIssue({
          severity: 'critical',
          category: 'Missing Organization Column',
          description: `Table ${table} is missing organization_id column`,
          table,
          recommendation: `Add organization_id column: ALTER TABLE public.${table} ADD COLUMN organization_id UUID REFERENCES public.organizations(id);`
        });
      }
    }
  }

  async auditOrphanedData() {
    console.log('\n🔍 Auditing for Orphaned Data...');

    // Check for tasks without organization_id
    const { data: tasksWithoutOrg } = await supabase
      .from('tasks')
      .select('id, title')
      .is('organization_id', null);

    if (tasksWithoutOrg && tasksWithoutOrg.length > 0) {
      this.addIssue({
        severity: 'high',
        category: 'Orphaned Data',
        description: `Found ${tasksWithoutOrg.length} tasks without organization_id`,
        table: 'tasks',
        recommendation: 'Update tasks to have proper organization_id or delete orphaned records'
      });
    }

    // Check for profiles without organization_id
    const { data: profilesWithoutOrg } = await supabase
      .from('profiles')
      .select('id, email')
      .is('organization_id', null)
      .neq('account_type', 'supplier'); // Suppliers might not have organization_id

    if (profilesWithoutOrg && profilesWithoutOrg.length > 0) {
      this.addIssue({
        severity: 'high',
        category: 'Orphaned Data',
        description: `Found ${profilesWithoutOrg.length} non-supplier profiles without organization_id`,
        table: 'profiles',
        recommendation: 'Update profiles to have proper organization_id'
      });
    }
  }

  async auditCrossOrganizationReferences() {
    console.log('\n🔍 Auditing for Cross-Organization References...');

    // Check for tasks assigned to users from different organizations
    const { data: crossOrgAssignments } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT t.id as task_id, t.title, t.organization_id as task_org, p.organization_id as assignee_org
        FROM public.tasks t
        JOIN public.profiles p ON t.assigned_to = p.id
        WHERE t.organization_id != p.organization_id
        AND t.organization_id IS NOT NULL
        AND p.organization_id IS NOT NULL
      `
    });

    if (crossOrgAssignments && crossOrgAssignments.length > 0) {
      this.addIssue({
        severity: 'critical',
        category: 'Cross-Organization Assignment',
        description: `Found ${crossOrgAssignments.length} tasks assigned to users from different organizations`,
        table: 'tasks',
        recommendation: 'Review and fix task assignments to ensure they are within the same organization'
      });
    }

    // Check for task messages from users in different organizations than the task
    const { data: crossOrgMessages } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT tm.id as message_id, t.organization_id as task_org, p.organization_id as sender_org
        FROM public.task_messages tm
        JOIN public.tasks t ON tm.task_id = t.id
        JOIN public.profiles p ON tm.sender_id = p.id
        WHERE t.organization_id != p.organization_id
        AND t.organization_id IS NOT NULL
        AND p.organization_id IS NOT NULL
        AND p.account_type != 'supplier'
      `
    });

    if (crossOrgMessages && crossOrgMessages.length > 0) {
      this.addIssue({
        severity: 'high',
        category: 'Cross-Organization Messages',
        description: `Found ${crossOrgMessages.length} messages from users in different organizations than the task`,
        table: 'task_messages',
        recommendation: 'Review message permissions and ensure proper organization isolation'
      });
    }

    // Check for tasks created by users from different organizations (critical security issue)
    const { data: crossOrgTaskCreation } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT t.id as task_id, t.title, t.organization_id as task_org, p.organization_id as creator_org
        FROM public.tasks t
        JOIN public.profiles p ON t.user_id = p.id
        WHERE t.organization_id != p.organization_id
        AND t.organization_id IS NOT NULL
        AND p.organization_id IS NOT NULL
        AND p.is_site_admin = false
      `
    });

    if (crossOrgTaskCreation && crossOrgTaskCreation.length > 0) {
      this.addIssue({
        severity: 'critical',
        category: 'Cross-Organization Task Creation',
        description: `Found ${crossOrgTaskCreation.length} tasks created by users in different organizations`,
        table: 'tasks',
        recommendation: 'CRITICAL: Review task creation permissions and fix organization assignment immediately'
      });
    }
  }

  async auditGetStreamSecurity() {
    console.log('\n🔍 Auditing GetStream Security Configuration...');

    // Check if GetStream environment variables are properly set
    const requiredEnvVars = ['GETSTREAM_API_KEY', 'GETSTREAM_API_SECRET'];

    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar] && !process.env[`VITE_${envVar}`]) {
        this.addIssue({
          severity: 'medium',
          category: 'Environment Configuration',
          description: `Missing GetStream environment variable: ${envVar}`,
          recommendation: `Set ${envVar} in environment variables`
        });
      }
    }

    // Check for tasks with GetStream channels but no organization validation
    const { data: tasksWithChannels } = await supabase
      .from('tasks')
      .select('id, getstream_channel_id, organization_id')
      .not('getstream_channel_id', 'is', null);

    if (tasksWithChannels) {
      const tasksWithoutOrg = tasksWithChannels.filter(task => !task.organization_id);
      if (tasksWithoutOrg.length > 0) {
        this.addIssue({
          severity: 'high',
          category: 'GetStream Security',
          description: `Found ${tasksWithoutOrg.length} tasks with GetStream channels but no organization_id`,
          table: 'tasks',
          recommendation: 'Ensure all tasks with chat channels have proper organization_id'
        });
      }
    }
  }

  async auditSupabaseConfiguration() {
    console.log('\n🔍 Auditing Supabase Configuration...');

    // Check if service role key is being used appropriately
    if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
      this.addIssue({
        severity: 'critical',
        category: 'Configuration',
        description: 'SUPABASE_SERVICE_ROLE_KEY is not set',
        recommendation: 'Set SUPABASE_SERVICE_ROLE_KEY in environment variables'
      });
    }

    // Check if anon key is not being used for admin operations
    if (!process.env.VITE_SUPABASE_ANON_KEY) {
      this.addIssue({
        severity: 'medium',
        category: 'Configuration',
        description: 'VITE_SUPABASE_ANON_KEY is not set',
        recommendation: 'Set VITE_SUPABASE_ANON_KEY for client-side operations'
      });
    }
  }

  async runFullAudit() {
    console.log('🚀 Starting Security Audit...\n');

    await this.auditRLSPolicies();
    await this.auditOrganizationColumns();
    await this.auditOrphanedData();
    await this.auditCrossOrganizationReferences();
    await this.auditGetStreamSecurity();
    await this.auditSupabaseConfiguration();

    console.log('\n📊 Security Audit Summary:');
    console.log(`Total issues found: ${this.issues.length}`);

    const severityCounts = this.issues.reduce((counts, issue) => {
      counts[issue.severity] = (counts[issue.severity] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);

    Object.entries(severityCounts).forEach(([severity, count]) => {
      console.log(`${severity.toUpperCase()}: ${count}`);
    });

    if (this.issues.length === 0) {
      console.log('✅ No security issues found!');
    } else {
      console.log('\n🔧 Recommendations:');
      this.issues.forEach((issue, index) => {
        console.log(`${index + 1}. [${issue.severity.toUpperCase()}] ${issue.recommendation}`);
      });
    }

    return this.issues;
  }
}

// Run the audit if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const auditor = new SecurityAuditor();
  auditor.runFullAudit()
    .then((issues) => {
      process.exit(issues.filter(i => i.severity === 'critical').length > 0 ? 1 : 0);
    })
    .catch((error) => {
      console.error('Security audit failed:', error);
      process.exit(1);
    });
}

export { SecurityAuditor };
