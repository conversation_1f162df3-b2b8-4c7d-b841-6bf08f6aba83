/**
 * <PERSON><PERSON><PERSON> to create a test image for the task flow test
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create a simple 1x1 pixel JPEG image
const jpegHeader = Buffer.from([
  0xFF, 0xD8, // SOI marker
  0xFF, 0xE0, // APP0 marker
  0x00, 0x10, // APP0 length
  0x4A, 0x46, 0x49, 0x46, 0x00, // JFIF identifier
  0x01, 0x01, // JFIF version
  0x00, // density units
  0x00, 0x01, // X density
  0x00, 0x01, // Y density
  0x00, 0x00, // thumbnail width/height
  0xFF, 0xDB, // DQT marker
  0x00, 0x43, // DQT length
  0x00, // table ID
  // Quantization table (64 bytes)
  0x10, 0x0B, 0x0C, 0x0E, 0x0C, 0x0A, 0x10, 0x0E,
  0x0D, 0x0E, 0x12, 0x11, 0x10, 0x13, 0x18, 0x28,
  0x1A, 0x18, 0x16, 0x16, 0x18, 0x31, 0x23, 0x25,
  0x1D, 0x28, 0x3A, 0x33, 0x3D, 0x3C, 0x39, 0x33,
  0x38, 0x37, 0x40, 0x48, 0x5C, 0x4E, 0x40, 0x44,
  0x57, 0x45, 0x37, 0x38, 0x50, 0x6D, 0x51, 0x57,
  0x5F, 0x62, 0x67, 0x68, 0x67, 0x3E, 0x4D, 0x71,
  0x79, 0x70, 0x64, 0x78, 0x5C, 0x65, 0x67, 0x63,
  0xFF, 0xC0, // SOF0 marker
  0x00, 0x0B, // SOF0 length
  0x08, // precision
  0x00, 0x01, // height
  0x00, 0x01, // width
  0x01, // number of components
  0x01, 0x11, 0x00, // component parameters
  0xFF, 0xC4, // DHT marker
  0x00, 0x1F, // DHT length
  0x00, // table class and ID
  // DC code lengths
  0x00, 0x01, 0x05, 0x01, 0x01, 0x01, 0x01, 0x01,
  0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  // DC symbols
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0A, 0x0B,
  0xFF, 0xC4, // DHT marker
  0x00, 0xB5, // DHT length
  0x10, // table class and ID
  // AC code lengths
  0x00, 0x02, 0x01, 0x03, 0x03, 0x02, 0x04, 0x03,
  0x05, 0x05, 0x04, 0x04, 0x00, 0x00, 0x01, 0x7D,
  // AC symbols
  0x01, 0x02, 0x03, 0x00, 0x04, 0x11, 0x05, 0x12,
  0x21, 0x31, 0x41, 0x06, 0x13, 0x51, 0x61, 0x07,
  0x22, 0x71, 0x14, 0x32, 0x81, 0x91, 0xA1, 0x08,
  0x23, 0x42, 0xB1, 0xC1, 0x15, 0x52, 0xD1, 0xF0,
  0x24, 0x33, 0x62, 0x72, 0x82, 0x09, 0x0A, 0x16,
  0x17, 0x18, 0x19, 0x1A, 0x25, 0x26, 0x27, 0x28,
  0x29, 0x2A, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39,
  0x3A, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49,
  0x4A, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59,
  0x5A, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69,
  0x6A, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x79,
  0x7A, 0x83, 0x84, 0x85, 0x86, 0x87, 0x88, 0x89,
  0x8A, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97, 0x98,
  0x99, 0x9A, 0xA2, 0xA3, 0xA4, 0xA5, 0xA6, 0xA7,
  0xA8, 0xA9, 0xAA, 0xB2, 0xB3, 0xB4, 0xB5, 0xB6,
  0xB7, 0xB8, 0xB9, 0xBA, 0xC2, 0xC3, 0xC4, 0xC5,
  0xC6, 0xC7, 0xC8, 0xC9, 0xCA, 0xD2, 0xD3, 0xD4,
  0xD5, 0xD6, 0xD7, 0xD8, 0xD9, 0xDA, 0xE1, 0xE2,
  0xE3, 0xE4, 0xE5, 0xE6, 0xE7, 0xE8, 0xE9, 0xEA,
  0xF1, 0xF2, 0xF3, 0xF4, 0xF5, 0xF6, 0xF7, 0xF8,
  0xF9, 0xFA,
  0xFF, 0xDA, // SOS marker
  0x00, 0x08, // SOS length
  0x01, // number of components
  0x01, 0x00, // component parameters
  0x00, 0x3F, 0x00, // spectral selection
  0xFF, 0xD9 // EOI marker
]);

// Create a blue pixel
const pixelData = Buffer.from([0x00, 0x00, 0xFF]); // RGB blue

// Combine the header and pixel data
const imageData = Buffer.concat([jpegHeader, pixelData]);

// Write the image to a file
fs.writeFileSync(path.join(__dirname, 'test-image.jpg'), imageData);

console.log('Test image created successfully!');
