-- SQL script to create a trigger that updates chat threads when task status changes

-- Create a function that will be called by the task status update trigger
CREATE OR REPLACE FUNCTION update_chat_threads_on_task_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Only proceed if status has changed
  IF OLD.status = NEW.status THEN
    RETURN NEW;
  END IF;

  -- Update all chat threads for this task to reflect the new task status
  UPDATE public.chat_threads
  SET 
    status = 
      CASE 
        -- If the task is assigned, set all threads to the appropriate status
        WHEN NEW.status = 'assigned' THEN
          CASE 
            -- The thread for the assigned supplier should be 'accepted'
            WHEN supplier_id = NEW.assigned_to THEN 'accepted'
            -- All other threads should be 'rejected'
            ELSE 'rejected'
          END
        -- For other task statuses, update the thread status to match
        WHEN NEW.status IN ('in_progress', 'completed', 'confirmed', 'approved', 'pending_payment') THEN
          -- Only update the thread for the assigned supplier
          CASE 
            WHEN supplier_id = NEW.assigned_to THEN NEW.status
            ELSE status -- Keep the current status for other threads
          END
        -- For open tasks, don't change thread statuses
        ELSE status
      END,
    updated_at = now()
  WHERE task_id = NEW.id;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the task status update trigger
DROP TRIGGER IF EXISTS update_chat_threads_on_task_status_change_trigger ON public.tasks;
CREATE TRIGGER update_chat_threads_on_task_status_change_trigger
AFTER UPDATE OF status ON public.tasks
FOR EACH ROW
EXECUTE FUNCTION update_chat_threads_on_task_status_change();

-- Add a comment to explain the trigger
COMMENT ON FUNCTION update_chat_threads_on_task_status_change() IS 'Updates chat threads when task status changes to ensure timeline consistency';
