/**
 * GetStream Comprehensive Test Suite
 *
 * This script tests all aspects of the GetStream implementation:
 * - Token generation
 * - User creation
 * - Channel creation
 * - Message sending
 * - System messages
 * - Channel queries
 *
 * Run with: node src/tests/getstream-comprehensive-test.js
 */

import fetch from 'node-fetch';
import { StreamChat } from 'stream-chat';

// Configuration
const BASE_URL = 'http://localhost:3000';
const API_KEY = process.env.VITE_GETSTREAM_API_KEY || process.env.GETSTREAM_API_KEY;
const TEST_PREFIX = 'test-' + Date.now();
const TEST_USER_ID = `${TEST_PREFIX}-user`;
const TEST_TASK_ID = `${TEST_PREFIX}-task`;
const TEST_TASK_TITLE = 'Test Task for Comprehensive Testing';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  total: 0,
};

// Helper function to log test results
function logTest(name, passed, message = '') {
  testResults.total++;

  if (passed) {
    testResults.passed++;
    console.log(`${colors.green}✓ PASS${colors.reset} ${name}`);
    if (message) {
      console.log(`  ${colors.dim}${message}${colors.reset}`);
    }
  } else {
    testResults.failed++;
    console.log(`${colors.red}✗ FAIL${colors.reset} ${name}`);
    if (message) {
      console.log(`  ${colors.red}${message}${colors.reset}`);
    }
  }
}

// Helper function to log section headers
function logSection(title) {
  console.log(`\n${colors.bright}${colors.cyan}=== ${title} ===${colors.reset}`);
}

// Test the token endpoint
async function testTokenGeneration() {
  logSection('Testing Token Generation');

  try {
    const response = await fetch(`${BASE_URL}/api/getstream/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId: TEST_USER_ID }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();

    if (data.token) {
      logTest('Token generation', true, `Token: ${data.token.substring(0, 20)}...`);
      return data.token;
    } else {
      logTest('Token generation', false, 'No token in response');
      return null;
    }
  } catch (error) {
    logTest('Token generation', false, error.message);
    return null;
  }
}

// Test direct client connection
async function testClientConnection(token) {
  logSection('Testing Direct Client Connection');

  if (!token) {
    logTest('Client connection', false, 'No token available');
    return null;
  }

  try {
    // Create a client instance
    const client = StreamChat.getInstance(API_KEY);

    // Connect the user
    await client.connectUser(
      {
        id: TEST_USER_ID,
        name: TEST_USER_ID,
      },
      token
    );

    logTest('Client connection', true, `Connected as: ${TEST_USER_ID}`);

    // Test disconnection
    await client.disconnectUser();
    logTest('Client disconnection', true);

    return client;
  } catch (error) {
    logTest('Client connection', false, error.message);
    return null;
  }
}

// Test channel creation
async function testChannelCreation() {
  logSection('Testing Channel Creation');

  try {
    const response = await fetch(`${BASE_URL}/api/getstream/channels`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        taskId: TEST_TASK_ID,
        taskTitle: TEST_TASK_TITLE,
        members: [TEST_USER_ID],
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();

    if (data.channelId) {
      logTest('Channel creation', true, `Channel ID: ${data.channelId}, Status: ${data.status}`);
      return data.channelId;
    } else {
      logTest('Channel creation', false, 'No channelId in response');
      return null;
    }
  } catch (error) {
    logTest('Channel creation', false, error.message);
    return null;
  }
}

// Test system message
async function testSystemMessage(channelId) {
  logSection('Testing System Messages');

  if (!channelId) {
    logTest('System message', false, 'No channel ID available');
    return false;
  }

  try {
    const response = await fetch(`${BASE_URL}/api/getstream/system-message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        channelId,
        text: `This is a test system message from the comprehensive test suite (${TEST_PREFIX})`,
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success) {
      logTest('System message', true, 'Message sent successfully');
      return true;
    } else {
      logTest('System message', false, 'Not successful');
      return false;
    }
  } catch (error) {
    logTest('System message', false, error.message);
    return false;
  }
}

// Test channel query with client
async function testChannelQuery(token, channelId) {
  logSection('Testing Channel Query');

  if (!token || !channelId) {
    logTest('Channel query', false, 'No token or channel ID available');
    return null;
  }

  try {
    // Create a client instance
    const client = StreamChat.getInstance(API_KEY);

    // Connect the user
    await client.connectUser(
      {
        id: TEST_USER_ID,
        name: TEST_USER_ID,
      },
      token
    );

    // Get the channel
    const channel = client.channel('messaging', channelId);

    // Query the channel
    const state = await channel.watch();

    // Check if the channel has messages
    const hasMessages = state.messages && state.messages.length > 0;

    logTest('Channel query', true, `Channel found with ${state.messages.length} messages`);

    // Disconnect the user
    await client.disconnectUser();

    return state;
  } catch (error) {
    logTest('Channel query', false, error.message);
    return null;
  }
}

// Run all tests
async function runTests() {
  console.log(`${colors.bright}${colors.magenta}=== GetStream Comprehensive Test Suite ===${colors.reset}`);
  console.log(`Base URL: ${BASE_URL}`);
  console.log(`Test Prefix: ${TEST_PREFIX}`);
  console.log(`Test User ID: ${TEST_USER_ID}`);
  console.log(`Test Task ID: ${TEST_TASK_ID}`);
  console.log(`${colors.magenta}======================================${colors.reset}\n`);

  // Run tests in sequence
  const token = await testTokenGeneration();
  await testClientConnection(token);
  const channelId = await testChannelCreation();
  await testSystemMessage(channelId);
  await testChannelQuery(token, channelId);

  // Print summary
  logSection('Test Summary');
  console.log(`Total tests: ${testResults.total}`);
  console.log(`${colors.green}Passed: ${testResults.passed}${colors.reset}`);
  console.log(`${colors.red}Failed: ${testResults.failed}${colors.reset}`);

  if (testResults.failed === 0) {
    console.log(`\n${colors.green}${colors.bright}All tests passed!${colors.reset}`);
  } else {
    console.log(`\n${colors.red}${colors.bright}Some tests failed!${colors.reset}`);
  }
}

// Run the tests
runTests();
