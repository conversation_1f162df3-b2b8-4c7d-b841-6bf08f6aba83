import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Award, Star, Clock, CheckCircle, Zap, Trophy, ThumbsUp, Heart } from 'lucide-react';

// Define badge types
export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  dateEarned: string;
  unlocked: boolean;
}

interface ProfileBadgesProps {
  userId: string;
  accountType: string;
  tasksCreated?: number;
  tasksCompleted?: number;
  reviewsReceived?: number;
  memberSince: string;
}

const ProfileBadges: React.FC<ProfileBadgesProps> = ({
  userId,
  accountType,
  tasksCreated = 0,
  tasksCompleted = 0,
  reviewsReceived = 0,
  memberSince
}) => {
  // Calculate membership duration in months
  const membershipMonths = Math.max(
    1,
    Math.floor(
      (new Date().getTime() - new Date(memberSince).getTime()) / (1000 * 60 * 60 * 24 * 30)
    )
  );

  // Define badges based on user type and achievements
  const getBadges = (): Badge[] => {
    const now = new Date().toISOString();
    const isSchool = accountType === 'school';
    const isSupplier = accountType === 'supplier';
    
    const commonBadges = [
      {
        id: 'early-adopter',
        name: 'Early Adopter',
        description: 'Joined Classtasker in its early days',
        icon: <Zap size={24} />,
        color: 'text-purple-500 bg-purple-100',
        dateEarned: memberSince,
        unlocked: true
      },
      {
        id: 'member-3-months',
        name: '3 Month Member',
        description: 'Been a member for 3 months',
        icon: <Clock size={24} />,
        color: 'text-blue-500 bg-blue-100',
        dateEarned: membershipMonths >= 3 ? now : '',
        unlocked: membershipMonths >= 3
      },
      {
        id: 'member-6-months',
        name: '6 Month Member',
        description: 'Been a member for 6 months',
        icon: <Clock size={24} />,
        color: 'text-blue-600 bg-blue-100',
        dateEarned: membershipMonths >= 6 ? now : '',
        unlocked: membershipMonths >= 6
      },
      {
        id: 'member-1-year',
        name: '1 Year Member',
        description: 'Been a member for 1 year',
        icon: <Trophy size={24} />,
        color: 'text-amber-500 bg-amber-100',
        dateEarned: membershipMonths >= 12 ? now : '',
        unlocked: membershipMonths >= 12
      }
    ];

    const schoolBadges = [
      {
        id: 'first-task',
        name: 'First Task',
        description: 'Created your first task',
        icon: <Star size={24} />,
        color: 'text-green-500 bg-green-100',
        dateEarned: tasksCreated > 0 ? now : '',
        unlocked: tasksCreated > 0
      },
      {
        id: 'task-master',
        name: 'Task Master',
        description: 'Created 10 tasks',
        icon: <Award size={24} />,
        color: 'text-indigo-500 bg-indigo-100',
        dateEarned: tasksCreated >= 10 ? now : '',
        unlocked: tasksCreated >= 10
      },
      {
        id: 'super-organizer',
        name: 'Super Organizer',
        description: 'Created 25 tasks',
        icon: <Award size={24} />,
        color: 'text-indigo-600 bg-indigo-100',
        dateEarned: tasksCreated >= 25 ? now : '',
        unlocked: tasksCreated >= 25
      }
    ];

    const supplierBadges = [
      {
        id: 'first-completion',
        name: 'First Completion',
        description: 'Completed your first task',
        icon: <CheckCircle size={24} />,
        color: 'text-green-500 bg-green-100',
        dateEarned: tasksCompleted > 0 ? now : '',
        unlocked: tasksCompleted > 0
      },
      {
        id: 'reliable-supplier',
        name: 'Reliable Supplier',
        description: 'Completed 10 tasks',
        icon: <ThumbsUp size={24} />,
        color: 'text-blue-500 bg-blue-100',
        dateEarned: tasksCompleted >= 10 ? now : '',
        unlocked: tasksCompleted >= 10
      },
      {
        id: 'top-supplier',
        name: 'Top Supplier',
        description: 'Completed 25 tasks',
        icon: <Trophy size={24} />,
        color: 'text-amber-500 bg-amber-100',
        dateEarned: tasksCompleted >= 25 ? now : '',
        unlocked: tasksCompleted >= 25
      },
      {
        id: 'highly-rated',
        name: 'Highly Rated',
        description: 'Received 5 positive reviews',
        icon: <Heart size={24} />,
        color: 'text-red-500 bg-red-100',
        dateEarned: reviewsReceived >= 5 ? now : '',
        unlocked: reviewsReceived >= 5
      }
    ];

    return [
      ...commonBadges,
      ...(isSchool ? schoolBadges : []),
      ...(isSupplier ? supplierBadges : [])
    ];
  };

  const badges = getBadges();
  const unlockedBadges = badges.filter(badge => badge.unlocked);
  const lockedBadges = badges.filter(badge => !badge.unlocked);

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">Achievements & Badges</CardTitle>
      </CardHeader>
      <CardContent>
        {unlockedBadges.length === 0 ? (
          <p className="text-sm text-gray-500 italic">No badges earned yet. Complete tasks to earn badges!</p>
        ) : (
          <div className="flex flex-wrap gap-3 mb-4">
            <TooltipProvider>
              {unlockedBadges.map((badge) => (
                <Tooltip key={badge.id}>
                  <TooltipTrigger asChild>
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${badge.color} cursor-help`}>
                      {badge.icon}
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="text-sm">
                      <p className="font-bold">{badge.name}</p>
                      <p>{badge.description}</p>
                      <p className="text-xs text-gray-500 mt-1">
                        Earned: {new Date(badge.dateEarned).toLocaleDateString()}
                      </p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              ))}
            </TooltipProvider>
          </div>
        )}

        {lockedBadges.length > 0 && (
          <>
            <div className="text-sm font-medium text-gray-500 mb-2">Badges to unlock:</div>
            <div className="flex flex-wrap gap-3">
              <TooltipProvider>
                {lockedBadges.map((badge) => (
                  <Tooltip key={badge.id}>
                    <TooltipTrigger asChild>
                      <div className="w-10 h-10 rounded-full flex items-center justify-center bg-gray-100 text-gray-400 cursor-help opacity-50">
                        {badge.icon}
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="text-sm">
                        <p className="font-bold">{badge.name}</p>
                        <p>{badge.description}</p>
                        <p className="text-xs text-gray-500 mt-1">Not yet unlocked</p>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                ))}
              </TooltipProvider>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default ProfileBadges;
