/**
 * TaskFactory for ClassTasker
 *
 * This service is responsible for creating typed task objects from database records.
 * It handles the conversion between database representation and application types.
 */

import { supabase } from '@/integrations/supabase/client';
import { Task, InternalTask, ExternalTask, isInternalTask, isExternalTask } from '@/types/tasks';

/**
 * TaskFactory class for creating task objects
 */
export class TaskFactory {
  /**
   * Creates a typed task object from a database record
   * @param data The database record
   * @returns A typed task object
   */
  static createFromDatabase(data: any): Task {
    // Create base task with common properties
    const baseTask = {
      id: data.id,
      title: data.title,
      description: data.description,
      location: data.location,
      location_formatted: data.location_formatted,
      location_lat: data.location_lat,
      location_lng: data.location_lng,
      location_place_id: data.location_place_id,
      building: data.building,
      room: data.room,
      category: data.category,
      budget: data.budget,
      due_date: data.due_date,
      created_at: data.created_at,
      updated_at: data.updated_at,
      user_id: data.user_id,
      organization_id: data.organization_id,
      // Add organization name if available from the join
      organization_name: data.organization?.name || null,
      // Add assigned user name if available from the join
      assigned_to_name: data.assigned_to_profile ?
        `${data.assigned_to_profile.first_name || ''} ${data.assigned_to_profile.last_name || ''}`.trim() :
        data.assigned_to_name || null,
      // Add creator name if available
      creator_name: data.creator_name || null,
      // Add images if available
      images: data.images || [],
    };

    // Determine task type based on data
    // First check explicit type field (new approach)
    // Then fall back to visibility field (backward compatibility)
    // For tasks with conflicting type and visibility, prioritize visibility
    const isInternal = data.visibility === 'internal' || (data.type === 'internal' && data.visibility !== 'public');

    // Special case for admin visibility tasks created by teachers
    // These should be treated as "pending" tasks, not yet classified as internal or external
    const isAdminReview = data.visibility === 'admin' && data.status === 'open';

    if (isInternal) {
      return {
        ...baseTask,
        type: 'internal',
        visibility: 'internal',
        status: data.status || 'assigned',
        assigned_to: data.assigned_to || '',
        assigned_role: data.assigned_role || '',
        payment_status: 'not_required',
        offers_count: 0
      } as InternalTask;
    } else if (isAdminReview) {
      // For admin review tasks, we'll create a special type of task
      // that doesn't show as either internal or external yet
      return {
        ...baseTask,
        type: 'pending', // Special type for tasks pending admin review
        visibility: 'admin',
        status: data.status || 'open',
        assigned_to: data.assigned_to || null,
        payment_status: data.payment_status || 'not_required',
        offers_count: data.offers_count || 0
      } as ExternalTask; // Still using ExternalTask interface for compatibility
    } else {
      return {
        ...baseTask,
        type: 'external',
        visibility: data.visibility || 'public',
        status: data.status || 'open',
        assigned_to: data.assigned_to || null,
        payment_status: data.payment_status || 'not_required',
        offers_count: data.offers_count || 0
      } as ExternalTask;
    }
  }

  /**
   * Creates a database record from a task object
   * @param task The task object
   * @returns A database record
   */
  static createForDatabase(task: Task): any {
    // Common fields for all tasks
    const dbTask = {
      title: task.title,
      description: task.description,
      location: task.location,
      location_formatted: task.location_formatted,
      location_lat: task.location_lat,
      location_lng: task.location_lng,
      location_place_id: task.location_place_id,
      building: task.building,
      room: task.room,
      category: task.category,
      budget: task.budget,
      due_date: task.due_date,
      user_id: task.user_id,
      organization_id: task.organization_id,
      type: task.type,
      visibility: task.visibility,
      status: task.status,
      assigned_to: task.assigned_to,
      images: task.images,
      updated_at: new Date().toISOString()
    };

    // Add type-specific fields
    if (isInternalTask(task)) {
      return {
        ...dbTask,
        assigned_role: task.assigned_role,
        payment_status: 'not_required'
      };
    } else {
      return {
        ...dbTask,
        payment_status: task.payment_status
      };
    }
  }

  /**
   * Helper method to determine role from user ID
   * @param userId The user ID
   * @returns The user's role
   */
  static async determineRoleFromUser(userId: string): Promise<string> {
    if (!userId) return 'unknown';

    try {
      const { data } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', userId)
        .single();

      return data?.role || 'unknown';
    } catch (error) {
      console.error('Error determining role from user:', error);
      return 'unknown';
    }
  }

  /**
   * Creates a new task with the appropriate type based on visibility
   * @param formData The form data for the new task
   * @returns A new task object
   */
  static async createFromFormData(formData: any): Promise<Task> {
    // Determine task type based on visibility
    const taskType = formData.visibility === 'internal' ? 'internal' : 'external';

    // Add assigned_role if it's an internal task with assignment
    let assigned_role = '';
    if (taskType === 'internal' && formData.assigned_to) {
      // Fetch role from profiles table
      assigned_role = await this.determineRoleFromUser(formData.assigned_to);
    }

    // Create base task
    const baseTask = {
      title: formData.title,
      description: formData.description,
      location: formData.location,
      location_formatted: formData.location_formatted,
      location_lat: formData.location_lat,
      location_lng: formData.location_lng,
      location_place_id: formData.location_place_id,
      building: formData.building,
      room: formData.room,
      category: formData.category,
      budget: formData.budget,
      due_date: formData.due_date,
      user_id: formData.user_id,
      organization_id: formData.organization_id,
      images: formData.images || [],
    };

    // Create appropriate task type
    if (taskType === 'internal') {
      return {
        ...baseTask,
        type: 'internal',
        visibility: 'internal',
        status: 'assigned',
        assigned_to: formData.assigned_to || '',
        assigned_role,
        payment_status: 'not_required',
        offers_count: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      } as InternalTask;
    } else {
      return {
        ...baseTask,
        type: 'external',
        visibility: formData.visibility || 'public',
        status: 'open',
        assigned_to: null,
        payment_status: 'not_required',
        offers_count: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      } as ExternalTask;
    }
  }
}
