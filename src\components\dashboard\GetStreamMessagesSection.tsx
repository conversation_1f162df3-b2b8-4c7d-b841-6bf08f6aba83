import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MessageSquare, Loader2, RefreshCw } from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useAuth } from '@/contexts/AuthContext';
import { Link } from 'react-router-dom';
import { Skeleton } from '@/components/ui/skeleton';
import { supabase } from '@/integrations/supabase/client';
import { StreamChat } from 'stream-chat';
import {
  getStreamClient,
  connectUser,
  disconnectUser,
  incrementConnectionCount,
  decrementConnectionCount,
  syncUserChannelsWithTasks,
  getAllUserTaskChannels
} from '@/integrations/getstream/client';
import { isPWA } from '@/utils/pwa-utils';
import { Chat, Channel, ChannelHeader, MessageInput, MessageList, Window } from 'stream-chat-react';

// Import GetStream CSS
import 'stream-chat-react/dist/css/v2/index.css';
import '@stream-io/stream-chat-css/dist/v2/css/index.css';

// Import custom CSS for GetStream chat
import '@/styles/getstream-chat.css';

interface ChatChannel {
  id: string;
  name: string;
  taskId?: string;
  lastMessage?: string;
  lastMessageTime?: string;
  unreadCount?: number;
}

const GetStreamMessagesSection = () => {
  const [channels, setChannels] = useState<ChatChannel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedChannelId, setSelectedChannelId] = useState<string | null>(null);
  const [streamClient, setStreamClient] = useState<StreamChat | null>(null);
  const [activeChannel, setActiveChannel] = useState<any>(null);
  const { user, profile } = useAuth();

  // Initialize GetStream client
  useEffect(() => {
    if (!user) return;

    const initializeStreamClient = async () => {
      try {
        setIsLoading(true);

        // Increment connection count
        incrementConnectionCount();

        // Get user display name
        const userName = profile?.first_name && profile?.last_name
          ? `${profile.first_name} ${profile.last_name}`
          : user.email?.split('@')[0] || 'User';

        // Check if we're in PWA mode
        const isPWA = typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches;
        console.log('[GetStreamMessagesSection] Initializing Stream client, PWA mode:', isPWA);

        // Connect to GetStream with retry logic
        let client;
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            console.log(`[GetStreamMessagesSection] Connecting to Stream (attempt ${retryCount + 1}/${maxRetries})`);
            client = await connectUser(
              user.id,
              userName,
              user.id, // This is just a placeholder, the actual token is generated server-side
              profile?.avatar_url || undefined
            );

            // If we get here, connection was successful
            console.log('[GetStreamMessagesSection] Successfully connected to Stream');
            break;
          } catch (connectError) {
            retryCount++;
            console.error(`[GetStreamMessagesSection] Error connecting to Stream (attempt ${retryCount}/${maxRetries}):`, connectError);

            if (retryCount >= maxRetries) {
              // Decrement connection count on final failure
              decrementConnectionCount(false);
              throw connectError; // Re-throw if we've exhausted retries
            }

            // Wait before retrying (exponential backoff)
            const delay = Math.min(1000 * Math.pow(2, retryCount), 8000);
            console.log(`[GetStreamMessagesSection] Retrying in ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }

        if (!client) {
          throw new Error('Failed to connect to Stream after multiple attempts');
        }

        setStreamClient(client);

        // Verify client is connected before querying channels
        if (!client.isConnected()) {
          console.warn('[GetStreamMessagesSection] Client reports as not connected, attempting to reconnect');

          // Try to reconnect
          await client.connectUser(
            {
              id: user.id,
              name: userName,
              image: profile?.avatar_url || undefined
            },
            await client.tokenProvider.getToken()
          );
        }

        console.log('[GetStreamMessagesSection] Querying channels');

        // OPTIMIZATION: Skip extra API calls for faster loading
        // Note: We'll rely on GetStream's direct channel query instead of syncing
        console.log('[GetStreamMessagesSection] Using direct GetStream query for maximum speed');

        // Also get channels from GetStream directly
        const filter = { type: 'messaging', members: { $in: [user.id] } };
        const sort = { last_message_at: -1 };

        // LIGHTWEIGHT APPROACH: Get channel list without full state (like real messaging apps)
        const streamChannels = await client.queryChannels(filter, sort, {
          limit: 20,           // ✅ Reduced limit for faster loading
          state: false,        // ✅ No full state loading
          watch: false,        // ✅ No real-time subscriptions for list
          message_limit: 1,    // ✅ Only last message for preview
        });

        console.log(`[GetStreamMessagesSection] Found ${streamChannels.length} stream channels`);

        // SECURITY: Filter channels based on organization membership (OPTIMIZED)
        const { data: userProfile } = await supabase
          .from('profiles')
          .select('organization_id, account_type')
          .eq('id', user.id)
          .single();

        const secureStreamChannels = [];

        // Separate task and non-task channels
        const taskStreamChannels = streamChannels.filter(channel => channel.data?.task_id);
        const nonTaskStreamChannels = streamChannels.filter(channel => !channel.data?.task_id);

        // Add non-task channels immediately
        secureStreamChannels.push(...nonTaskStreamChannels);

        if (taskStreamChannels.length > 0) {
          // Batch query all tasks at once
          const taskIds = taskStreamChannels.map(channel => channel.data.task_id);
          const { data: tasks, error: tasksError } = await supabase
            .from('tasks')
            .select('id, organization_id, visibility')
            .in('id', taskIds);

          if (tasksError) {
            console.error('[GetStreamMessagesSection] Error fetching tasks for security filtering:', tasksError);
          } else if (tasks) {
            const taskMap = new Map(tasks.map(task => [task.id, task]));

            for (const channel of taskStreamChannels) {
              const taskId = channel.data.task_id;
              const task = taskMap.get(taskId);

              if (!task) {
                console.warn('[GetStreamMessagesSection] Task not found for channel:', channel.id);
                continue;
              }

              const hasAccess = (
                userProfile?.organization_id === task.organization_id ||
                (userProfile?.account_type === 'supplier' && task.visibility === 'public')
              );

              if (hasAccess) {
                secureStreamChannels.push(channel);
              } else {
                console.warn('[GetStreamMessagesSection] Filtering out unauthorized channel:', {
                  channelId: channel.id,
                  taskId,
                  userOrg: userProfile?.organization_id,
                  taskOrg: task.organization_id
                });
              }
            }
          }
        }

        console.log('[GetStreamMessagesSection] Secure channels after filtering:', secureStreamChannels.length);

        // OPTIMIZATION: Use secure channels directly (no complex merging)
        const allChannels = secureStreamChannels;
        console.log(`[GetStreamMessagesSection] Using ${allChannels.length} secure channels directly`);

        // LIGHTWEIGHT FORMAT: Format channels for display without heavy operations
        const formattedChannels = allChannels.map(channel => {
          // Extract task ID from channel ID (format: task-{taskId})
          const taskId = channel.id.startsWith('task-') ? channel.id.substring(5) : channel.data?.task_id;

          // Use lightweight channel name (will be updated when chat is opened)
          let channelName = channel.data?.name || 'Chat';
          if (taskId && (!channelName || channelName === 'Chat')) {
            channelName = 'Task Chat'; // Placeholder, will be updated on-demand
          }

          // Get last message from lightweight data
          const lastMessage = channel.state?.messages?.[0]?.text ||
                             channel.data?.last_message_text ||
                             'No messages yet';

          return {
            id: channel.id,
            name: channelName,
            taskId: taskId,
            lastMessage,
            lastMessageTime: channel.data?.last_message_at,
            unreadCount: 0 // Will be calculated when chat is opened
          };
        });

        // Sort by last message time
        formattedChannels.sort((a, b) => {
          if (!a.lastMessageTime) return 1;
          if (!b.lastMessageTime) return -1;
          return new Date(b.lastMessageTime).getTime() - new Date(a.lastMessageTime).getTime();
        });

        setChannels(formattedChannels);

        // OPTIMIZATION: No auto-selection for faster loading (user clicks to load)
        // This prevents automatic chat loading which slows down initial load
        console.log('[GetStreamMessagesSection] ✅ Lightweight initialization complete - no auto-selection');

        setIsLoading(false);
      } catch (error) {
        console.error('[GetStreamMessagesSection] Error initializing Stream client:', error);
        setIsLoading(false);

        // Decrement connection count on error
        decrementConnectionCount(false);
      }
    };

    initializeStreamClient();

    return () => {
      console.log('[GetStreamMessagesSection] Component unmounting');

      // Decrement connection count and let the manager handle disconnection
      try {
        decrementConnectionCount(true);
      } catch (error) {
        console.warn('[GetStreamMessagesSection] Error in cleanup function:', error);
      }
    };
  }, [user, profile]);

  // Handle channel selection with ON-DEMAND LOADING
  const handleSelectChannel = async (channelId: string) => {
    if (!streamClient) {
      console.error('[GetStreamMessagesSection] Cannot select channel: Stream client is not initialized');
      return;
    }

    try {
      console.log(`[GetStreamMessagesSection] 🔄 Loading full chat for channel: ${channelId}`);
      setSelectedChannelId(channelId);

      // Check if client is connected
      if (!streamClient.isConnected()) {
        console.warn('[GetStreamMessagesSection] Client not connected, attempting to reconnect');

        // Get user display name
        const userName = profile?.first_name && profile?.last_name
          ? `${profile.first_name} ${profile.last_name}`
          : user.email?.split('@')[0] || 'User';

        try {
          // Try to reconnect
          await streamClient.connectUser(
            {
              id: user.id,
              name: userName,
              image: profile?.avatar_url || undefined
            },
            await streamClient.tokenProvider.getToken()
          );
          console.log('[GetStreamMessagesSection] Successfully reconnected client');
        } catch (reconnectError) {
          console.error('[GetStreamMessagesSection] Failed to reconnect client:', reconnectError);

          // Create a new client as a last resort
          console.log('[GetStreamMessagesSection] Creating new client as fallback');
          const newClient = await connectUser(
            user.id,
            userName,
            user.id,
            profile?.avatar_url || undefined
          );

          setStreamClient(newClient);
        }
      }

      // NOW we load the full channel state (on-demand)
      const channel = streamClient.channel('messaging', channelId);

      // Watch the channel to get full state and real-time updates
      console.log(`[GetStreamMessagesSection] 📡 Watching channel for full state: ${channelId}`);
      await channel.watch();

      // Update channel name with task title if needed (on-demand)
      const taskId = channel.data?.task_id ||
        (channel.id.startsWith('task-') ? channel.id.replace('task-', '') : null);

      if (taskId && (!channel.data?.name || channel.data.name === 'Chat' || channel.data.name === 'Task Chat')) {
        try {
          const { data: taskData } = await supabase
            .from('tasks')
            .select('title')
            .eq('id', taskId)
            .single();

          if (taskData?.title) {
            await channel.update({
              name: taskData.title,
              task_id: taskId
            });

            // Update the channel in our list
            setChannels(prev => prev.map(ch =>
              ch.id === channelId ? { ...ch, name: taskData.title } : ch
            ));
          }
        } catch (error) {
          console.error('[GetStreamMessagesSection] Error updating channel name:', error);
        }
      }

      console.log(`[GetStreamMessagesSection] ✅ Successfully loaded full chat: ${channelId}`);
      setActiveChannel(channel);
    } catch (error) {
      console.error('[GetStreamMessagesSection] ❌ Error loading chat:', error);
    }
  };

  // Get initials for avatar
  const getInitials = (name: string): string => {
    if (!name) return 'C';
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Format date
  const formatDate = (dateString?: string): string => {
    if (!dateString) return '';

    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold mb-4">Messages</h2>
          <div className="flex justify-center py-4">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle refresh
  const handleRefresh = async () => {
    if (!user || !streamClient || isRefreshing) return;

    setIsRefreshing(true);

    try {
      console.log('[GetStreamMessagesSection] Refreshing channels (optimized)');

      // OPTIMIZATION: Skip extra API calls for faster refresh
      console.log('[GetStreamMessagesSection] Using direct GetStream query for fast refresh');

      // Also get channels from GetStream directly
      const filter = { type: 'messaging', members: { $in: [user.id] } };
      const sort = { last_message_at: -1 };

      // LIGHTWEIGHT APPROACH: Get channel list without full state (refresh)
      const streamChannels = await streamClient.queryChannels(filter, sort, {
        limit: 20,           // ✅ Reduced limit for faster refresh
        state: false,        // ✅ No full state loading
        watch: false,        // ✅ No real-time subscriptions for list
        message_limit: 1,    // ✅ Only last message for preview
      });

      console.log(`[GetStreamMessagesSection] Found ${streamChannels.length} stream channels`);

      // SECURITY: Filter channels based on organization membership (OPTIMIZED)
      const { data: userProfile } = await supabase
        .from('profiles')
        .select('organization_id, account_type')
        .eq('id', user.id)
        .single();

      const secureStreamChannels = [];

      // Separate task and non-task channels
      const taskStreamChannels = streamChannels.filter(channel => channel.data?.task_id);
      const nonTaskStreamChannels = streamChannels.filter(channel => !channel.data?.task_id);

      // Add non-task channels immediately
      secureStreamChannels.push(...nonTaskStreamChannels);

      if (taskStreamChannels.length > 0) {
        // Batch query all tasks at once
        const taskIds = taskStreamChannels.map(channel => channel.data.task_id);
        const { data: tasks, error: tasksError } = await supabase
          .from('tasks')
          .select('id, organization_id, visibility')
          .in('id', taskIds);

        if (tasksError) {
          console.error('[GetStreamMessagesSection] Error fetching tasks for security filtering:', tasksError);
        } else if (tasks) {
          const taskMap = new Map(tasks.map(task => [task.id, task]));

          for (const channel of taskStreamChannels) {
            const taskId = channel.data.task_id;
            const task = taskMap.get(taskId);

            if (!task) {
              console.warn('[GetStreamMessagesSection] Task not found for channel:', channel.id);
              continue;
            }

            const hasAccess = (
              userProfile?.organization_id === task.organization_id ||
              (userProfile?.account_type === 'supplier' && task.visibility === 'public')
            );

            if (hasAccess) {
              secureStreamChannels.push(channel);
            } else {
              console.warn('[GetStreamMessagesSection] Filtering out unauthorized channel:', {
                channelId: channel.id,
                taskId,
                userOrg: userProfile?.organization_id,
                taskOrg: task.organization_id
              });
            }
          }
        }
      }

      console.log('[GetStreamMessagesSection] Secure channels after filtering:', secureStreamChannels.length);

      // OPTIMIZATION: Use secure channels directly (no complex merging in refresh)
      const allChannels = secureStreamChannels;
      console.log(`[GetStreamMessagesSection] Using ${allChannels.length} secure channels directly (refresh)`);

      // LIGHTWEIGHT FORMAT: Format channels for display without heavy operations (refresh)
      const formattedChannels = allChannels.map(channel => {
        // Extract task ID from channel ID (format: task-{taskId})
        const taskId = channel.id.startsWith('task-') ? channel.id.substring(5) : channel.data?.task_id;

        // Use lightweight channel name (will be updated when chat is opened)
        let channelName = channel.data?.name || 'Chat';
        if (taskId && (!channelName || channelName === 'Chat')) {
          channelName = 'Task Chat'; // Placeholder, will be updated on-demand
        }

        // Get last message from lightweight data
        const lastMessage = channel.state?.messages?.[0]?.text ||
                           channel.data?.last_message_text ||
                           'No messages yet';

        return {
          id: channel.id,
          name: channelName,
          taskId: taskId,
          lastMessage,
          lastMessageTime: channel.data?.last_message_at,
          unreadCount: 0 // Will be calculated when chat is opened
        };
      });

      // Sort by last message time
      formattedChannels.sort((a, b) => {
        if (!a.lastMessageTime) return 1;
        if (!b.lastMessageTime) return -1;
        return new Date(b.lastMessageTime).getTime() - new Date(a.lastMessageTime).getTime();
      });

      setChannels(formattedChannels);

      console.log('[GetStreamMessagesSection] Refresh completed successfully');
    } catch (error) {
      console.error('[GetStreamMessagesSection] Error refreshing channels:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Messages</h2>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing || isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Channel List */}
          <div className="md:col-span-1 border rounded-md overflow-hidden">
            <div className="bg-gray-50 p-3 border-b">
              <h3 className="font-medium text-sm">Recent Conversations</h3>
            </div>
            <div className="divide-y overflow-y-auto" style={{ maxHeight: '400px' }}>
              {channels.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  <MessageSquare className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p>No conversations yet</p>
                </div>
              ) : (
                channels.map((channel) => (
                  <div
                    key={channel.id}
                    className={`p-3 cursor-pointer hover:bg-gray-50 ${
                      selectedChannelId === channel.id ? 'bg-blue-50' : ''
                    }`}
                    onClick={() => handleSelectChannel(channel.id)}
                  >
                    <div className="flex items-start">
                      <Avatar className="h-8 w-8 mr-3">
                        <AvatarFallback>{getInitials(channel.name)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="flex justify-between items-start">
                          <h4 className="font-medium text-sm truncate">{channel.name}</h4>
                          <span className="text-xs text-gray-500 whitespace-nowrap ml-2">
                            {formatDate(channel.lastMessageTime)}
                          </span>
                        </div>
                        <p className="text-xs text-gray-500 truncate mt-1">
                          {channel.lastMessage}
                        </p>
                        {channel.taskId && (
                          <Link
                            to={`/tasks/${channel.taskId}`}
                            className="text-xs text-classtasker-blue hover:underline mt-1 inline-block"
                            onClick={(e) => e.stopPropagation()}
                          >
                            View Task
                          </Link>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Chat Window */}
          <div className="md:col-span-2">
            {selectedChannelId && streamClient && activeChannel ? (
              <div className="border rounded-md overflow-hidden h-[400px] flex flex-col">
                <Chat client={streamClient} theme="messaging light">
                  <Channel channel={activeChannel}>
                    <Window>
                      <ChannelHeader />
                      <MessageList />
                      <MessageInput />
                    </Window>
                  </Channel>
                </Chat>
              </div>
            ) : (
              <div className="border rounded-md flex items-center justify-center p-8 h-[400px]">
                <p className="text-gray-500">
                  {channels.length === 0 ?
                    'You have no conversations yet' :
                    'Select a conversation to start messaging'}
                </p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default GetStreamMessagesSection;
