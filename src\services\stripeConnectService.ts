import { createClient } from '@supabase/supabase-js';
import Stripe from 'stripe';

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Initialize Stripe
const stripeSecretKey = import.meta.env.VITE_STRIPE_SECRET_KEY;
const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2023-10-16',
});

/**
 * Service for managing Stripe Connect accounts
 */
export const stripeConnectService = {
  /**
   * Get a user's Stripe Connect account ID
   * @param userId The user ID
   * @returns The Stripe Connect account ID, or null if not found
   */
  async getAccountId(userId: string): Promise<string | null> {
    try {
      // Check if the user already has a Stripe Connect account
      const { data: stripeAccounts, error: stripeAccountsError } = await supabase
        .from('stripe_connect_accounts')
        .select('stripe_account_id')
        .eq('user_id', userId)
        .limit(1);

      if (stripeAccountsError) {
        console.error('Error fetching Stripe Connect account:', stripeAccountsError);
        
        // If the stripe_connect_accounts table doesn't exist, return null
        if (stripeAccountsError.code === '42P01') {
          console.warn('stripe_connect_accounts table does not exist.');
          return null;
        }
        
        throw stripeAccountsError;
      }

      // If the user already has a Stripe Connect account, return it
      if (stripeAccounts && stripeAccounts.length > 0) {
        return stripeAccounts[0].stripe_account_id;
      }

      return null;
    } catch (error) {
      console.error('Error in getAccountId:', error);
      throw error;
    }
  },

  /**
   * Create a Stripe Connect account for a user
   * @param userId The user ID
   * @param email The user's email
   * @returns The Stripe Connect account
   */
  async createAccount(userId: string, email: string): Promise<Stripe.Account> {
    try {
      // Create a new Stripe Connect account
      const account = await stripe.accounts.create({
        type: 'express',
        email,
        capabilities: {
          card_payments: { requested: true },
          transfers: { requested: true },
        },
        metadata: {
          user_id: userId
        }
      });

      // Store the Stripe Connect account in the database
      try {
        await supabase
          .from('stripe_connect_accounts')
          .insert({
            user_id: userId,
            stripe_account_id: account.id,
            account_status: 'created',
            charges_enabled: account.charges_enabled,
            payouts_enabled: account.payouts_enabled,
            details_submitted: account.details_submitted,
          });
      } catch (error) {
        console.error('Error storing Stripe Connect account:', error);
        console.warn('Could not store Stripe Connect account in the database. The stripe_connect_accounts table might not exist.');
      }

      return account;
    } catch (error) {
      console.error('Error in createAccount:', error);
      throw error;
    }
  },

  /**
   * Get or create a Stripe Connect account for a user
   * @param userId The user ID
   * @param email The user's email
   * @returns The Stripe Connect account ID
   */
  async getOrCreateAccount(userId: string, email: string): Promise<string> {
    try {
      // Check if the user already has a Stripe Connect account
      const accountId = await this.getAccountId(userId);

      if (accountId) {
        // Verify that the account exists in Stripe
        try {
          await stripe.accounts.retrieve(accountId);
          return accountId;
        } catch (error) {
          console.error('Error retrieving Stripe Connect account:', error);
          console.log('Creating a new Stripe Connect account...');
        }
      }

      // Create a new Stripe Connect account
      const account = await this.createAccount(userId, email);

      return account.id;
    } catch (error) {
      console.error('Error in getOrCreateAccount:', error);
      throw error;
    }
  },

  /**
   * Create an account link for a user to onboard to Stripe Connect
   * @param userId The user ID
   * @param email The user's email
   * @param refreshUrl The URL to redirect to if the onboarding process is refreshed
   * @param returnUrl The URL to redirect to after the onboarding process is complete
   * @returns The account link URL
   */
  async createAccountLink(userId: string, email: string, refreshUrl: string, returnUrl: string): Promise<string> {
    try {
      // Get or create a Stripe Connect account for the user
      const accountId = await this.getOrCreateAccount(userId, email);

      // Create an account link
      const accountLink = await stripe.accountLinks.create({
        account: accountId,
        refresh_url: refreshUrl,
        return_url: returnUrl,
        type: 'account_onboarding',
      });

      return accountLink.url;
    } catch (error) {
      console.error('Error in createAccountLink:', error);
      throw error;
    }
  },

  /**
   * Update a user's Stripe Connect account status
   * @param userId The user ID
   * @returns True if the account was updated, false otherwise
   */
  async updateAccountStatus(userId: string): Promise<boolean> {
    try {
      // Get the user's Stripe Connect account ID
      const accountId = await this.getAccountId(userId);

      if (!accountId) {
        return false;
      }

      // Get the account details from Stripe
      const account = await stripe.accounts.retrieve(accountId);

      // Update the account status in the database
      try {
        await supabase
          .from('stripe_connect_accounts')
          .update({
            account_status: account.details_submitted ? 'details_submitted' : 'created',
            charges_enabled: account.charges_enabled,
            payouts_enabled: account.payouts_enabled,
            details_submitted: account.details_submitted,
            updated_at: new Date().toISOString(),
          })
          .eq('user_id', userId);
      } catch (error) {
        console.error('Error updating Stripe Connect account status:', error);
        console.warn('Could not update Stripe Connect account status in the database. The stripe_connect_accounts table might not exist.');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in updateAccountStatus:', error);
      throw error;
    }
  },

  /**
   * Create a login link for a user to access their Stripe Connect dashboard
   * @param userId The user ID
   * @returns The login link URL
   */
  async createLoginLink(userId: string): Promise<string> {
    try {
      // Get the user's Stripe Connect account ID
      const accountId = await this.getAccountId(userId);

      if (!accountId) {
        throw new Error(`No Stripe Connect account found for user ${userId}`);
      }

      // Create a login link
      const loginLink = await stripe.accounts.createLoginLink(accountId);

      return loginLink.url;
    } catch (error) {
      console.error('Error in createLoginLink:', error);
      throw error;
    }
  },

  /**
   * Delete a user's Stripe Connect account
   * @param userId The user ID
   * @returns True if the account was deleted, false otherwise
   */
  async deleteAccount(userId: string): Promise<boolean> {
    try {
      // Get the user's Stripe Connect account ID
      const accountId = await this.getAccountId(userId);

      if (!accountId) {
        return false;
      }

      // Delete the Stripe Connect account
      await stripe.accounts.del(accountId);

      // Delete the Stripe Connect account from the database
      try {
        await supabase
          .from('stripe_connect_accounts')
          .delete()
          .eq('user_id', userId);
      } catch (error) {
        console.error('Error deleting Stripe Connect account from database:', error);
        console.warn('Could not delete Stripe Connect account from the database. The stripe_connect_accounts table might not exist.');
      }

      return true;
    } catch (error) {
      console.error('Error in deleteAccount:', error);
      throw error;
    }
  }
};

export default stripeConnectService;
